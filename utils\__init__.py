"""
Rigaddon Play - Utils Module
Core utilities and animation engine
"""

from . import animation_core
from . import playback
from . import json_loader

def register():
    """Register utils module"""
    animation_core.register()
    playback.register()
    json_loader.register()

def unregister():
    """Unregister utils module"""
    json_loader.unregister()
    playback.unregister()
    animation_core.unregister()
