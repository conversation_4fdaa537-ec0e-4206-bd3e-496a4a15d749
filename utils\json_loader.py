"""
Rigaddon Play - JSON Loader Utilities
Utilities for loading and managing JSON data
"""

import json
import os
from typing import Dict, Any, Optional

class RGP_JSONLoader:
    """JSON loader and validator for Rigaddon Play"""

    @staticmethod
    def load_json_file(file_path: str) -> Optional[Dict[str, Any]]:
        """Load JSON data from file"""
        try:
            if not os.path.exists(file_path):
                print(f"RGP: JSON file not found: {file_path}")
                return None

            with open(file_path, 'r', encoding='utf-8') as file:
                data = json.load(file)

            print(f"RGP: Successfully loaded JSON from {file_path}")
            return data

        except json.JSONDecodeError as e:
            print(f"RGP: JSON decode error in {file_path}: {e}")
            return None
        except Exception as e:
            print(f"RGP: Error loading JSON file {file_path}: {e}")
            return None

    @staticmethod
    def save_json_file(file_path: str, data: Dict[str, Any]) -> bool:
        """Save JSON data to file"""
        try:
            # Create directory if it doesn't exist
            os.makedirs(os.path.dirname(file_path), exist_ok=True)

            with open(file_path, 'w', encoding='utf-8') as file:
                json.dump(data, file, indent=2, ensure_ascii=False)

            print(f"RGP: Successfully saved JSON to {file_path}")
            return True

        except Exception as e:
            print(f"RGP: Error saving JSON file {file_path}: {e}")
            return False

    @staticmethod
    def validate_preset_json(data: Dict[str, Any]) -> bool:
        """Validate preset JSON structure"""
        if not isinstance(data, dict):
            print("RGP: Preset data must be a dictionary")
            return False

        # Check for required top-level keys
        required_keys = ['animation_presets']
        for key in required_keys:
            if key not in data:
                print(f"RGP: Missing required key: {key}")
                return False

        # Validate animation presets
        presets = data.get('animation_presets', {})
        if not isinstance(presets, dict):
            print("RGP: animation_presets must be a dictionary")
            return False

        for preset_name, preset_data in presets.items():
            if not RGP_JSONLoader.validate_single_preset(preset_name, preset_data):
                return False

        return True

    @staticmethod
    def validate_single_preset(name: str, preset_data: Dict[str, Any]) -> bool:
        """Validate a single preset structure"""
        if not isinstance(preset_data, dict):
            print(f"RGP: Preset '{name}' data must be a dictionary")
            return False

        # Required fields for a preset
        required_fields = ['name', 'description', 'category', 'keyframes']
        for field in required_fields:
            if field not in preset_data:
                print(f"RGP: Preset '{name}' missing required field: {field}")
                return False

        # Validate keyframes
        keyframes = preset_data.get('keyframes', [])
        if not isinstance(keyframes, list):
            print(f"RGP: Preset '{name}' keyframes must be a list")
            return False

        if len(keyframes) == 0:
            print(f"RGP: Preset '{name}' must have at least one keyframe")
            return False

        for i, keyframe in enumerate(keyframes):
            if not isinstance(keyframe, dict):
                print(f"RGP: Preset '{name}' keyframe {i} must be a dictionary")
                return False

            if 'frame' not in keyframe:
                print(f"RGP: Preset '{name}' keyframe {i} missing 'frame' field")
                return False

        return True

    @staticmethod
    def merge_preset_data(base_data: Dict[str, Any], new_data: Dict[str, Any]) -> Dict[str, Any]:
        """Merge new preset data with existing data"""
        merged = base_data.copy()

        if 'animation_presets' in new_data:
            if 'animation_presets' not in merged:
                merged['animation_presets'] = {}

            merged['animation_presets'].update(new_data['animation_presets'])

        # Merge other top-level keys
        for key, value in new_data.items():
            if key != 'animation_presets':
                merged[key] = value

        return merged

def register():
    """Register JSON loader utilities"""
    print("RGP: JSON loader utilities registered")

def unregister():
    """Unregister JSON loader utilities"""
    print("RGP: JSON loader utilities unregistered")