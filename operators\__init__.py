"""
Rigaddon Play - Operators Module
All operators for animation control and management
"""

from . import add_animation
from . import play_animation
from . import remove_animation
from . import custom_controls

def register():
    """Register operators module"""
    add_animation.register()
    play_animation.register()
    remove_animation.register()
    custom_controls.register()

def unregister():
    """Unregister operators module"""
    custom_controls.unregister()
    remove_animation.unregister()
    play_animation.unregister()
    add_animation.unregister()
