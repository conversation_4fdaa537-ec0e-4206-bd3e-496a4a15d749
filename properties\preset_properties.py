"""
Rigaddon Play - Preset Properties
Properties for managing animation presets
"""

import bpy
from bpy.props import StringProperty, IntProperty, FloatProperty, BoolProperty, EnumProperty
from bpy.types import PropertyGroup

class RGP_PresetProperty(PropertyGroup):
    """Individual preset property for dynamic UI generation"""

    rgp_prop_name: StringProperty(
        name="Property Name",
        description="Name of the property",
        default=""
    )

    rgp_prop_type: EnumProperty(
        name="Property Type",
        description="Type of the property",
        items=[
            ('FLOAT', 'Float', 'Float property'),
            ('INT', 'Integer', 'Integer property'),
            ('BOOL', 'Boolean', 'Boolean property'),
            ('STRING', 'String', 'String property'),
            ('ENUM', 'Enum', 'Enumeration property'),
        ],
        default='FLOAT'
    )

    rgp_float_value: FloatProperty(
        name="Float Value",
        description="Float value for this property",
        default=0.0
    )

    rgp_int_value: IntProperty(
        name="Integer Value",
        description="Integer value for this property",
        default=0
    )

    rgp_bool_value: BoolProperty(
        name="Boolean Value",
        description="Boolean value for this property",
        default=False
    )

    rgp_string_value: StringProperty(
        name="String Value",
        description="String value for this property",
        default=""
    )

    rgp_enum_value: StringProperty(
        name="Enum Value",
        description="Selected enum value",
        default=""
    )

    rgp_min_value: FloatProperty(
        name="Minimum Value",
        description="Minimum value for numeric properties",
        default=0.0
    )

    rgp_max_value: FloatProperty(
        name="Maximum Value",
        description="Maximum value for numeric properties",
        default=1.0
    )

    rgp_description: StringProperty(
        name="Description",
        description="Property description",
        default=""
    )

class RGP_PresetInstance(PropertyGroup):
    """Instance of a preset with customized properties"""

    rgp_preset_name: StringProperty(
        name="Preset Name",
        description="Name of the base preset",
        default=""
    )

    rgp_instance_name: StringProperty(
        name="Instance Name",
        description="Custom name for this preset instance",
        default=""
    )

    rgp_properties: bpy.props.CollectionProperty(
        type=RGP_PresetProperty,
        name="Properties"
    )

    rgp_is_custom: BoolProperty(
        name="Is Custom",
        description="Whether this is a custom preset",
        default=False
    )

    rgp_category: StringProperty(
        name="Category",
        description="Preset category",
        default="Custom"
    )

# Registration
classes = [
    RGP_PresetProperty,
    RGP_PresetInstance,
]

def register():
    """Register preset properties"""
    from bpy.utils import register_class

    for cls in classes:
        register_class(cls)

    print("RGP: Preset properties registered")

def unregister():
    """Unregister preset properties"""
    from bpy.utils import unregister_class

    for cls in reversed(classes):
        unregister_class(cls)

    print("RGP: Preset properties unregistered")