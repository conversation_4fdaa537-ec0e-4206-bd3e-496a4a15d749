"""
Rigaddon Play - Professional Animation Addon for Blender 4.3
Created by: <PERSON><PERSON><PERSON>
Category: Rigaddon

A comprehensive animation toolkit with preset-based animations,
professional UI, and advanced animation controls.
"""

bl_info = {
    "name": "Rigaddon Play",
    "author": "<PERSON><PERSON><PERSON>",
    "version": (1, 0, 0),
    "blender": (4, 3, 0),
    "location": "View3D > UI > Rigaddon > Rigaddon Play",
    "description": "Professional animation toolkit with preset-based animations and advanced controls",
    "category": "Rigaddon",
    "doc_url": "",
    "tracker_url": "",
}

import bpy
from bpy.utils import register_class, unregister_class
from bpy.types import AddonPreferences
from bpy.props import StringProperty, BoolProperty

# Import modules
from . import properties
from . import operators
from . import ui
from . import utils
from . import presets
from . import menus

# Addon preferences
class RGP_AddonPreferences(AddonPreferences):
    bl_idname = __name__

    rgp_preset_path: StringProperty(
        name="Preset Path",
        description="Path to animation presets directory",
        default="",
        subtype='DIR_PATH'
    )

    rgp_auto_keyframe: Bool<PERSON>roperty(
        name="Auto Keyframe",
        description="Automatically set keyframes when applying animations",
        default=True
    )

    rgp_show_advanced: BoolProperty(
        name="Show Advanced Options",
        description="Show advanced animation options in UI",
        default=False
    )

    def draw(self, context):
        layout = self.layout

        box = layout.box()
        box.label(text="Rigaddon Play Settings", icon='SETTINGS')

        col = box.column()
        col.prop(self, "rgp_preset_path")
        col.prop(self, "rgp_auto_keyframe")
        col.prop(self, "rgp_show_advanced")

# Classes to register
classes = [
    RGP_AddonPreferences,
]

def register():
    """Register all addon classes and properties"""
    # Register preferences first
    for cls in classes:
        register_class(cls)

    # Register modules
    properties.register()
    operators.register()
    ui.register()
    utils.register()
    presets.register()
    menus.register()

    print("Rigaddon Play: Addon registered successfully")

def unregister():
    """Unregister all addon classes and properties"""
    # Unregister modules in reverse order
    menus.unregister()
    presets.unregister()
    utils.unregister()
    ui.unregister()
    operators.unregister()
    properties.unregister()

    # Unregister preferences
    for cls in reversed(classes):
        unregister_class(cls)

    print("Rigaddon Play: Addon unregistered successfully")

if __name__ == "__main__":
    register()