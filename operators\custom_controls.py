"""
Rigaddon Play - Custom Controls Operators
Operators for advanced animation controls and utilities
"""

import bpy
from bpy.types import Operator
from bpy.props import StringProperty, IntProperty, BoolProperty, FloatProperty
import webbrowser
from ..utils.animation_core import get_animation_engine

class RGP_OT_OpenDocumentation(Operator):
    """Open addon documentation"""
    bl_idname = "rgp.open_documentation"
    bl_label = "Open Documentation"
    bl_description = "Open Rigaddon Play documentation in web browser"

    def execute(self, context):
        try:
            # This would open the actual documentation URL
            documentation_url = "https://github.com/rigel-tapangan/rigaddon-play"
            webbrowser.open(documentation_url)

            self.report({'INFO'}, "Documentation opened in web browser")
            return {'FINISHED'}

        except Exception as e:
            self.report({'ERROR'}, f"Error opening documentation: {str(e)}")
            return {'CANCELLED'}

class RGP_OT_ExportAnimations(Operator):
    """Export animations to JSON file"""
    bl_idname = "rgp.export_animations"
    bl_label = "Export Animations"
    bl_description = "Export current animations to a JSON file"
    bl_options = {'REGISTER'}

    filepath: StringProperty(
        name="File Path",
        description="Path to save the animation file",
        default="rigaddon_animations.json",
        subtype='FILE_PATH'
    )

    def invoke(self, context, event):
        context.window_manager.fileselect_add(self)
        return {'RUNNING_MODAL'}

    def execute(self, context):
        try:
            import json
            scene_props = context.scene.rgp_props

            # Prepare export data
            export_data = {
                "rigaddon_play_version": "1.0.0",
                "animations": []
            }

            for anim in scene_props.rgp_animations:
                anim_data = {
                    "name": anim.rgp_name,
                    "preset_name": anim.rgp_preset_name,
                    "target_object": anim.rgp_target_object,
                    "start_frame": anim.rgp_start_frame,
                    "end_frame": anim.rgp_end_frame,
                    "duration": anim.rgp_duration,
                    "is_active": anim.rgp_is_active,
                    "is_looping": anim.rgp_is_looping,
                    "loop_count": anim.rgp_loop_count,
                    "custom_properties": {
                        "float_1": anim.rgp_custom_float_1,
                        "float_2": anim.rgp_custom_float_2,
                        "float_3": anim.rgp_custom_float_3,
                        "int_1": anim.rgp_custom_int_1,
                        "int_2": anim.rgp_custom_int_2,
                        "string_1": anim.rgp_custom_string_1,
                        "bool_1": anim.rgp_custom_bool_1,
                        "bool_2": anim.rgp_custom_bool_2
                    },
                    "keyframes": []
                }

                # Export keyframes
                for kf in anim.rgp_keyframes:
                    kf_data = {
                        "frame": kf.rgp_frame,
                        "location": list(kf.rgp_location),
                        "rotation": list(kf.rgp_rotation),
                        "scale": list(kf.rgp_scale),
                        "alpha": kf.rgp_alpha,
                        "easing": kf.rgp_easing
                    }
                    anim_data["keyframes"].append(kf_data)

                export_data["animations"].append(anim_data)

            # Save to file
            with open(self.filepath, 'w', encoding='utf-8') as file:
                json.dump(export_data, file, indent=2, ensure_ascii=False)

            self.report({'INFO'}, f"Exported {len(export_data['animations'])} animations to {self.filepath}")
            return {'FINISHED'}

        except Exception as e:
            self.report({'ERROR'}, f"Error exporting animations: {str(e)}")
            return {'CANCELLED'}

class RGP_OT_ImportAnimations(Operator):
    """Import animations from JSON file"""
    bl_idname = "rgp.import_animations"
    bl_label = "Import Animations"
    bl_description = "Import animations from a JSON file"
    bl_options = {'REGISTER', 'UNDO'}

    filepath: StringProperty(
        name="File Path",
        description="Path to the animation file to import",
        default="",
        subtype='FILE_PATH'
    )

    replace_existing: BoolProperty(
        name="Replace Existing",
        description="Replace existing animations with imported ones",
        default=False
    )

    def invoke(self, context, event):
        context.window_manager.fileselect_add(self)
        return {'RUNNING_MODAL'}

    def execute(self, context):
        try:
            import json
            scene_props = context.scene.rgp_props

            # Load file
            with open(self.filepath, 'r', encoding='utf-8') as file:
                import_data = json.load(file)

            # Validate data
            if "animations" not in import_data:
                self.report({'ERROR'}, "Invalid animation file format")
                return {'CANCELLED'}

            # Clear existing if requested
            if self.replace_existing:
                scene_props.rgp_animations.clear()

            # Import animations
            imported_count = 0
            for anim_data in import_data["animations"]:
                anim = scene_props.rgp_animations.add()

                # Import basic properties
                anim.rgp_name = anim_data.get("name", "Imported Animation")
                anim.rgp_preset_name = anim_data.get("preset_name", "")
                anim.rgp_target_object = anim_data.get("target_object", "")
                anim.rgp_start_frame = anim_data.get("start_frame", 1)
                anim.rgp_end_frame = anim_data.get("end_frame", 30)
                anim.rgp_duration = anim_data.get("duration", 30)
                anim.rgp_is_active = anim_data.get("is_active", True)
                anim.rgp_is_looping = anim_data.get("is_looping", False)
                anim.rgp_loop_count = anim_data.get("loop_count", 1)

                # Import custom properties
                custom_props = anim_data.get("custom_properties", {})
                anim.rgp_custom_float_1 = custom_props.get("float_1", 0.0)
                anim.rgp_custom_float_2 = custom_props.get("float_2", 0.0)
                anim.rgp_custom_float_3 = custom_props.get("float_3", 0.0)
                anim.rgp_custom_int_1 = custom_props.get("int_1", 0)
                anim.rgp_custom_int_2 = custom_props.get("int_2", 0)
                anim.rgp_custom_string_1 = custom_props.get("string_1", "")
                anim.rgp_custom_bool_1 = custom_props.get("bool_1", False)
                anim.rgp_custom_bool_2 = custom_props.get("bool_2", False)

                # Import keyframes
                for kf_data in anim_data.get("keyframes", []):
                    kf = anim.rgp_keyframes.add()
                    kf.rgp_frame = kf_data.get("frame", 1)
                    kf.rgp_location = kf_data.get("location", [0, 0, 0])
                    kf.rgp_rotation = kf_data.get("rotation", [0, 0, 0])
                    kf.rgp_scale = kf_data.get("scale", [1, 1, 1])
                    kf.rgp_alpha = kf_data.get("alpha", 1.0)
                    kf.rgp_easing = kf_data.get("easing", "EASE_IN_OUT")

                imported_count += 1

            # Set active animation
            if len(scene_props.rgp_animations) > 0:
                scene_props.rgp_active_animation_index = len(scene_props.rgp_animations) - 1

            self.report({'INFO'}, f"Imported {imported_count} animations from {self.filepath}")
            return {'FINISHED'}

        except Exception as e:
            self.report({'ERROR'}, f"Error importing animations: {str(e)}")
            return {'CANCELLED'}

class RGP_OT_SetPlaybackMode(Operator):
    """Set playback mode"""
    bl_idname = "rgp.set_playback_mode"
    bl_label = "Set Playback Mode"
    bl_description = "Set the playback mode for animations"
    bl_options = {'REGISTER'}

    mode: StringProperty(
        name="Mode",
        description="Playback mode to set",
        default="SINGLE"
    )

    def execute(self, context):
        try:
            scene_props = context.scene.rgp_props
            scene_props.rgp_playback_mode = self.mode

            mode_names = {
                'SINGLE': 'Single',
                'ALL': 'All',
                'FROM_SELECTED': 'From Selected',
                'LOOP_SELECTED': 'Loop Selected'
            }

            self.report({'INFO'}, f"Playback mode set to: {mode_names.get(self.mode, self.mode)}")
            return {'FINISHED'}

        except Exception as e:
            self.report({'ERROR'}, f"Error setting playback mode: {str(e)}")
            return {'CANCELLED'}

class RGP_OT_SetEasing(Operator):
    """Set easing type for selected animation"""
    bl_idname = "rgp.set_easing"
    bl_label = "Set Easing"
    bl_description = "Set easing type for the selected animation"
    bl_options = {'REGISTER', 'UNDO'}

    easing_type: StringProperty(
        name="Easing Type",
        description="Type of easing to apply",
        default="EASE_IN_OUT"
    )

    def execute(self, context):
        try:
            scene_props = context.scene.rgp_props

            # Get active animation
            if len(scene_props.rgp_animations) == 0:
                self.report({'ERROR'}, "No animations available")
                return {'CANCELLED'}

            if scene_props.rgp_active_animation_index >= len(scene_props.rgp_animations):
                self.report({'ERROR'}, "No animation selected")
                return {'CANCELLED'}

            active_anim = scene_props.rgp_animations[scene_props.rgp_active_animation_index]

            # Update all keyframes with new easing
            for keyframe in active_anim.rgp_keyframes:
                keyframe.rgp_easing = self.easing_type

            # Get target object and update its animation
            target_obj = active_anim.rgp_target_object
            if target_obj and target_obj.animation_data and target_obj.animation_data.action:
                action = target_obj.animation_data.action

                # Update easing for all fcurves in the action
                for fcurve in action.fcurves:
                    for keyframe in fcurve.keyframe_points:
                        # Map easing types to Blender's interpolation
                        if self.easing_type == 'LINEAR':
                            keyframe.interpolation = 'LINEAR'
                        elif self.easing_type in ['EASE_IN', 'EASE_OUT', 'EASE_IN_OUT']:
                            keyframe.interpolation = 'BEZIER'
                            if hasattr(keyframe, 'easing'):
                                keyframe.easing = self.easing_type
                        elif self.easing_type in ['BOUNCE', 'ELASTIC', 'BACK']:
                            keyframe.interpolation = self.easing_type

            self.report({'INFO'}, f"Applied {self.easing_type} easing to '{active_anim.rgp_name}'")
            return {'FINISHED'}

        except Exception as e:
            self.report({'ERROR'}, f"Error setting easing: {str(e)}")
            return {'CANCELLED'}

class RGP_OT_RestoreTransform(Operator):
    """Restore object to original transform"""
    bl_idname = "rgp.restore_transform"
    bl_label = "Restore Transform"
    bl_description = "Restore object to its original transform before animation"
    bl_options = {'REGISTER', 'UNDO'}

    animation_index: IntProperty(
        name="Animation Index",
        description="Index of animation to restore transform for",
        default=-1
    )

    def execute(self, context):
        try:
            scene_props = context.scene.rgp_props
            animation_engine = get_animation_engine()

            # Determine which animation to use
            if self.animation_index >= 0:
                index = self.animation_index
            else:
                index = scene_props.rgp_active_animation_index

            # Validate index
            if index < 0 or index >= len(scene_props.rgp_animations):
                self.report({'ERROR'}, "No animation selected")
                return {'CANCELLED'}

            animation_item = scene_props.rgp_animations[index]

            # Check if backup exists
            if not animation_item.rgp_has_backup:
                self.report({'ERROR'}, f"No transform backup available for '{animation_item.rgp_name}'")
                return {'CANCELLED'}

            # Get target object
            target_obj = animation_item.rgp_target_object
            if not target_obj:
                self.report({'ERROR'}, f"No target object set for animation '{animation_item.rgp_name}'")
                return {'CANCELLED'}

            # Check if object still exists in scene
            if target_obj.name not in bpy.data.objects:
                self.report({'ERROR'}, f"Target object '{target_obj.name}' no longer exists")
                return {'CANCELLED'}

            # Restore transform
            transform_backup = {
                'location': animation_item.rgp_backup_location,
                'rotation_euler': animation_item.rgp_backup_rotation,
                'scale': animation_item.rgp_backup_scale
            }

            animation_engine.restore_object_transform(target_obj, transform_backup)

            self.report({'INFO'}, f"Restored transform for '{target_obj.name}'")
            return {'FINISHED'}

        except Exception as e:
            self.report({'ERROR'}, f"Error restoring transform: {str(e)}")
            return {'CANCELLED'}

class RGP_OT_DuplicateAnimation(Operator):
    """Duplicate selected animation"""
    bl_idname = "rgp.duplicate_animation"
    bl_label = "Duplicate Animation"
    bl_description = "Create a copy of the selected animation"
    bl_options = {'REGISTER', 'UNDO'}

    animation_index: IntProperty(
        name="Animation Index",
        description="Index of animation to duplicate",
        default=-1
    )

    def execute(self, context):
        try:
            scene_props = context.scene.rgp_props

            # Determine which animation to duplicate
            if self.animation_index >= 0:
                index = self.animation_index
            else:
                index = scene_props.rgp_active_animation_index

            # Validate index
            if index < 0 or index >= len(scene_props.rgp_animations):
                self.report({'ERROR'}, "No animation selected")
                return {'CANCELLED'}

            source_anim = scene_props.rgp_animations[index]

            # Create duplicate
            new_anim = scene_props.rgp_animations.add()

            # Copy all properties
            new_anim.rgp_name = f"{source_anim.rgp_name} Copy"
            new_anim.rgp_preset_name = source_anim.rgp_preset_name
            new_anim.rgp_target_object = source_anim.rgp_target_object
            new_anim.rgp_start_frame = source_anim.rgp_start_frame
            new_anim.rgp_end_frame = source_anim.rgp_end_frame
            new_anim.rgp_duration = source_anim.rgp_duration
            new_anim.rgp_is_active = source_anim.rgp_is_active
            new_anim.rgp_is_looping = source_anim.rgp_is_looping
            new_anim.rgp_loop_count = source_anim.rgp_loop_count

            # Copy custom properties
            new_anim.rgp_custom_float_1 = source_anim.rgp_custom_float_1
            new_anim.rgp_custom_float_2 = source_anim.rgp_custom_float_2
            new_anim.rgp_custom_float_3 = source_anim.rgp_custom_float_3
            new_anim.rgp_custom_int_1 = source_anim.rgp_custom_int_1
            new_anim.rgp_custom_int_2 = source_anim.rgp_custom_int_2
            new_anim.rgp_custom_string_1 = source_anim.rgp_custom_string_1
            new_anim.rgp_custom_bool_1 = source_anim.rgp_custom_bool_1
            new_anim.rgp_custom_bool_2 = source_anim.rgp_custom_bool_2

            # Copy backup transform
            new_anim.rgp_backup_location = source_anim.rgp_backup_location
            new_anim.rgp_backup_rotation = source_anim.rgp_backup_rotation
            new_anim.rgp_backup_scale = source_anim.rgp_backup_scale
            new_anim.rgp_has_backup = source_anim.rgp_has_backup

            # Copy keyframes
            for source_kf in source_anim.rgp_keyframes:
                new_kf = new_anim.rgp_keyframes.add()
                new_kf.rgp_frame = source_kf.rgp_frame
                new_kf.rgp_location = source_kf.rgp_location
                new_kf.rgp_rotation_euler = source_kf.rgp_rotation_euler
                new_kf.rgp_scale = source_kf.rgp_scale
                new_kf.rgp_easing = source_kf.rgp_easing

            # Set as active animation
            scene_props.rgp_active_animation_index = len(scene_props.rgp_animations) - 1

            self.report({'INFO'}, f"Duplicated animation '{source_anim.rgp_name}'")
            return {'FINISHED'}

        except Exception as e:
            self.report({'ERROR'}, f"Error duplicating animation: {str(e)}")
            return {'CANCELLED'}

class RGP_OT_SelectAnimationAndShowControls(Operator):
    """Select animation and switch to controls subtab"""
    bl_idname = "rgp.select_animation_and_show_controls"
    bl_label = "Select Animation and Show Controls"
    bl_description = "Select animation and automatically switch to controls subtab"
    bl_options = {'REGISTER'}

    animation_index: IntProperty(
        name="Animation Index",
        description="Index of animation to select",
        default=-1
    )

    def execute(self, context):
        try:
            scene_props = context.scene.rgp_props

            # Validate index
            if self.animation_index < 0 or self.animation_index >= len(scene_props.rgp_animations):
                self.report({'ERROR'}, "Invalid animation index")
                return {'CANCELLED'}

            # Set active animation
            scene_props.rgp_active_animation_index = self.animation_index

            # Switch to animations tab and controls subtab
            scene_props.rgp_ui_tab = 'ANIMATIONS'
            scene_props.rgp_animation_subtab = 'CONTROLS'

            animation_name = scene_props.rgp_animations[self.animation_index].rgp_name
            self.report({'INFO'}, f"Selected '{animation_name}' - Controls ready")
            return {'FINISHED'}

        except Exception as e:
            self.report({'ERROR'}, f"Error selecting animation: {str(e)}")
            return {'CANCELLED'}

class RGP_OT_ClearAllAnimations(Operator):
    """Clear all animations from the list"""
    bl_idname = "rgp.clear_all_animations"
    bl_label = "Clear All Animations"
    bl_description = "Remove all animations from the list"
    bl_options = {'REGISTER', 'UNDO'}

    def invoke(self, context, event):
        return context.window_manager.invoke_confirm(self, event)

    def execute(self, context):
        try:
            scene_props = context.scene.rgp_props

            # Stop all playing animations
            bpy.ops.screen.animation_cancel()

            # Clear keyframes for all animations
            cleared_objects = set()
            for anim in scene_props.rgp_animations:
                if anim.rgp_target_object and anim.rgp_target_object not in cleared_objects:
                    obj = anim.rgp_target_object

                    # Clear all keyframes for this object
                    if obj.animation_data and obj.animation_data.action:
                        obj.animation_data_clear()

                    # Restore original transform if backup exists
                    if anim.rgp_has_backup:
                        obj.location = anim.rgp_original_location
                        obj.rotation_euler = anim.rgp_original_rotation
                        obj.scale = anim.rgp_original_scale

                    cleared_objects.add(obj)

            # Clear all animations from list
            scene_props.rgp_animations.clear()
            scene_props.rgp_active_animation_index = 0

            # Update scene
            context.view_layer.update()

            self.report({'INFO'}, f"All animations cleared and keyframes removed from {len(cleared_objects)} objects")
            return {'FINISHED'}

        except Exception as e:
            self.report({'ERROR'}, f"Error clearing animations: {str(e)}")
            return {'CANCELLED'}

class RGP_OT_MoveAnimation(Operator):
    """Move animation up or down in the list"""
    bl_idname = "rgp.move_animation"
    bl_label = "Move Animation"
    bl_description = "Move animation up or down in the list"
    bl_options = {'REGISTER', 'UNDO'}

    direction: StringProperty(
        name="Direction",
        description="Direction to move animation",
        default="UP"
    )

    def execute(self, context):
        try:
            scene_props = context.scene.rgp_props

            if len(scene_props.rgp_animations) < 2:
                self.report({'WARNING'}, "Need at least 2 animations to move")
                return {'CANCELLED'}

            current_index = scene_props.rgp_active_animation_index

            if self.direction == 'UP':
                new_index = max(0, current_index - 1)
            else:  # DOWN
                new_index = min(len(scene_props.rgp_animations) - 1, current_index + 1)

            if new_index == current_index:
                self.report({'WARNING'}, f"Cannot move animation {self.direction.lower()}")
                return {'CANCELLED'}

            # Move the animation
            scene_props.rgp_animations.move(current_index, new_index)
            scene_props.rgp_active_animation_index = new_index

            self.report({'INFO'}, f"Moved animation {self.direction.lower()}")
            return {'FINISHED'}

        except Exception as e:
            self.report({'ERROR'}, f"Error moving animation: {str(e)}")
            return {'CANCELLED'}

class RGP_OT_DeleteCustomPreset(Operator):
    """Delete a custom preset"""
    bl_idname = "rgp.delete_custom_preset"
    bl_label = "Delete Custom Preset"
    bl_description = "Delete the selected custom preset"
    bl_options = {'REGISTER', 'UNDO'}

    preset_name: StringProperty(
        name="Preset Name",
        description="Name of preset to delete",
        default=""
    )

    def invoke(self, context, event):
        return context.window_manager.invoke_confirm(self, event)

    def execute(self, context):
        try:
            from ..presets.utils_presets import get_preset_manager
            preset_manager = get_preset_manager()

            # Check if preset exists and is custom
            preset_data = preset_manager.get_preset(self.preset_name)
            if not preset_data:
                self.report({'ERROR'}, f"Preset '{self.preset_name}' not found")
                return {'CANCELLED'}

            # Only allow deletion of custom presets
            if preset_data.get('category') != 'Custom':
                self.report({'ERROR'}, "Only custom presets can be deleted")
                return {'CANCELLED'}

            # Delete the preset
            success = preset_manager.delete_preset(self.preset_name)

            if success:
                self.report({'INFO'}, f"Deleted custom preset: {self.preset_name}")
                return {'FINISHED'}
            else:
                self.report({'ERROR'}, f"Failed to delete preset: {self.preset_name}")
                return {'CANCELLED'}

        except Exception as e:
            self.report({'ERROR'}, f"Error deleting preset: {str(e)}")
            return {'CANCELLED'}

class RGP_OT_ResetAllTransforms(Operator):
    """Reset all objects to their original transforms"""
    bl_idname = "rgp.reset_all_transforms"
    bl_label = "Reset All Transforms"
    bl_description = "Reset all objects with animations to their original transforms"
    bl_options = {'REGISTER', 'UNDO'}

    def invoke(self, context, event):
        return context.window_manager.invoke_confirm(self, event)

    def execute(self, context):
        try:
            scene_props = context.scene.rgp_props
            reset_count = 0

            # Reset all objects that have animations with backups
            for anim in scene_props.rgp_animations:
                if anim.rgp_target_object and anim.rgp_has_backup:
                    obj = anim.rgp_target_object
                    if obj:
                        # Restore original transform
                        obj.location = anim.rgp_original_location
                        obj.rotation_euler = anim.rgp_original_rotation
                        obj.scale = anim.rgp_original_scale

                        # Clear all keyframes for this object
                        if obj.animation_data and obj.animation_data.action:
                            obj.animation_data_clear()

                        reset_count += 1

            # Also reset any selected objects to their default transforms
            for obj in context.selected_objects:
                if obj.type in ['MESH', 'CURVE', 'SURFACE', 'META', 'FONT', 'ARMATURE', 'LATTICE', 'EMPTY', 'CAMERA', 'LIGHT']:
                    obj.location = (0, 0, 0)
                    obj.rotation_euler = (0, 0, 0)
                    obj.scale = (1, 1, 1)

                    # Clear keyframes
                    if obj.animation_data and obj.animation_data.action:
                        obj.animation_data_clear()

            # Update scene
            context.view_layer.update()

            if reset_count > 0:
                self.report({'INFO'}, f"Reset transforms for {reset_count} animated objects")
            else:
                self.report({'INFO'}, "Reset transforms for selected objects")

            return {'FINISHED'}

        except Exception as e:
            self.report({'ERROR'}, f"Error resetting transforms: {str(e)}")
            return {'CANCELLED'}

class RGP_OT_BakeAnimation(Operator):
    """Bake animation to keyframes"""
    bl_idname = "rgp.bake_animation"
    bl_label = "Bake Animation"
    bl_description = "Bake the selected animation to actual keyframes"
    bl_options = {'REGISTER', 'UNDO'}

    animation_index: IntProperty(
        name="Animation Index",
        description="Index of animation to bake",
        default=-1
    )

    frame_step: IntProperty(
        name="Frame Step",
        description="Step between baked keyframes",
        default=1,
        min=1,
        max=10
    )

    def execute(self, context):
        try:
            scene_props = context.scene.rgp_props

            # Get animation to bake
            if self.animation_index >= 0:
                index = self.animation_index
            else:
                index = scene_props.rgp_active_animation_index

            # Validate index
            if index < 0 or index >= len(scene_props.rgp_animations):
                self.report({'ERROR'}, "Invalid animation index")
                return {'CANCELLED'}

            animation_item = scene_props.rgp_animations[index]

            # Get target object
            target_obj = animation_item.rgp_target_object
            if not target_obj:
                self.report({'ERROR'}, f"No target object set for animation '{animation_item.rgp_name}'")
                return {'CANCELLED'}

            # Check if object still exists in scene
            if target_obj.name not in bpy.data.objects:
                self.report({'ERROR'}, f"Target object '{target_obj.name}' no longer exists")
                return {'CANCELLED'}

            # Bake animation
            start_frame = animation_item.rgp_start_frame
            end_frame = animation_item.rgp_end_frame

            # Select only the target object
            bpy.ops.object.select_all(action='DESELECT')
            target_obj.select_set(True)
            context.view_layer.objects.active = target_obj

            # Bake keyframes
            bpy.ops.nla.bake(
                frame_start=start_frame,
                frame_end=end_frame,
                step=self.frame_step,
                only_selected=True,
                visual_keying=True,
                clear_constraints=False,
                clear_parents=False,
                use_current_action=True,
                bake_types={'OBJECT'}
            )

            self.report({'INFO'}, f"Baked animation '{animation_item.rgp_name}' to keyframes")
            return {'FINISHED'}

        except Exception as e:
            self.report({'ERROR'}, f"Error baking animation: {str(e)}")
            return {'CANCELLED'}

# Registration
classes = [
    RGP_OT_OpenDocumentation,
    RGP_OT_ExportAnimations,
    RGP_OT_ImportAnimations,
    RGP_OT_SetPlaybackMode,
    RGP_OT_SetEasing,
    RGP_OT_RestoreTransform,
    RGP_OT_DuplicateAnimation,
    RGP_OT_SelectAnimationAndShowControls,
    RGP_OT_ClearAllAnimations,
    RGP_OT_MoveAnimation,
    RGP_OT_DeleteCustomPreset,
    RGP_OT_ResetAllTransforms,
    RGP_OT_BakeAnimation,
]

def register():
    """Register custom control operators"""
    from bpy.utils import register_class

    for cls in classes:
        register_class(cls)

    print("RGP: Custom control operators registered")

def unregister():
    """Unregister custom control operators"""
    from bpy.utils import unregister_class

    for cls in reversed(classes):
        unregister_class(cls)

    print("RGP: Custom control operators unregistered")