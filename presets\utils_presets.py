"""
Rigaddon Play - Preset Utilities
Utilities for loading, saving, and managing animation presets
"""

import json
import os
import bpy
from typing import Dict, List, Any, Optional

class RGP_PresetManager:
    """Manager class for handling animation presets"""

    def __init__(self):
        self.presets_data = {}
        self.preset_file_path = ""
        self.load_presets()

    def get_preset_file_path(self) -> str:
        """Get the path to the presets JSON file"""
        addon_dir = os.path.dirname(os.path.dirname(__file__))
        return os.path.join(addon_dir, "presets", "anim_presets.json")

    def load_presets(self) -> bool:
        """Load presets from JSON file"""
        try:
            self.preset_file_path = self.get_preset_file_path()

            if not os.path.exists(self.preset_file_path):
                print(f"RGP: Preset file not found: {self.preset_file_path}")
                return False

            with open(self.preset_file_path, 'r', encoding='utf-8') as file:
                self.presets_data = json.load(file)

            print(f"RGP: Loaded {len(self.get_animation_presets())} animation presets")
            return True

        except Exception as e:
            print(f"RGP: Error loading presets: {e}")
            return False

    def save_presets(self) -> bool:
        """Save current presets to JSON file"""
        try:
            with open(self.preset_file_path, 'w', encoding='utf-8') as file:
                json.dump(self.presets_data, file, indent=2, ensure_ascii=False)

            print("RGP: Presets saved successfully")
            return True

        except Exception as e:
            print(f"RGP: Error saving presets: {e}")
            return False

    def get_animation_presets(self) -> Dict[str, Any]:
        """Get all animation presets sorted alphabetically"""
        presets = self.presets_data.get("animation_presets", {})
        # Return as ordered dict sorted by keys
        from collections import OrderedDict
        return OrderedDict(sorted(presets.items()))

    def get_preset(self, preset_name: str) -> Optional[Dict[str, Any]]:
        """Get a specific preset by name"""
        presets = self.get_animation_presets()
        return presets.get(preset_name)

    def get_preset_names(self) -> List[str]:
        """Get list of all preset names sorted alphabetically"""
        return sorted(list(self.get_animation_presets().keys()))

    def get_categories(self) -> List[Dict[str, str]]:
        """Get list of preset categories sorted alphabetically"""
        categories = self.presets_data.get("categories", [])
        return sorted(categories, key=lambda x: x.get("name", ""))

    def get_presets_by_category(self, category: str) -> Dict[str, Any]:
        """Get all presets in a specific category sorted alphabetically"""
        presets = self.get_animation_presets()
        filtered_presets = {}

        for name, preset in presets.items():
            if preset.get("category") == category:
                filtered_presets[name] = preset

        # Return as ordered dict sorted by keys
        from collections import OrderedDict
        return OrderedDict(sorted(filtered_presets.items()))

    def get_easing_types(self) -> List[Dict[str, str]]:
        """Get list of available easing types"""
        return self.presets_data.get("easing_types", [])

    def add_custom_preset(self, name: str, preset_data: Dict[str, Any]) -> bool:
        """Add a new custom preset"""
        try:
            if "animation_presets" not in self.presets_data:
                self.presets_data["animation_presets"] = {}

            self.presets_data["animation_presets"][name] = preset_data
            return self.save_presets()

        except Exception as e:
            print(f"RGP: Error adding custom preset: {e}")
            return False

    def remove_preset(self, name: str) -> bool:
        """Remove a preset"""
        try:
            presets = self.get_animation_presets()
            if name in presets:
                del presets[name]
                return self.save_presets()
            return False

        except Exception as e:
            print(f"RGP: Error removing preset: {e}")
            return False

    def validate_preset(self, preset_data: Dict[str, Any]) -> bool:
        """Validate preset data structure"""
        required_fields = ["name", "description", "category", "keyframes"]

        for field in required_fields:
            if field not in preset_data:
                print(f"RGP: Missing required field: {field}")
                return False

        # Validate keyframes
        keyframes = preset_data.get("keyframes", [])
        if not isinstance(keyframes, list) or len(keyframes) == 0:
            print("RGP: Invalid keyframes data")
            return False

        for keyframe in keyframes:
            if not isinstance(keyframe, dict) or "frame" not in keyframe:
                print("RGP: Invalid keyframe structure")
                return False

        return True

# Global preset manager instance
rgp_preset_manager = RGP_PresetManager()

def get_preset_manager() -> RGP_PresetManager:
    """Get the global preset manager instance"""
    return rgp_preset_manager

def reload_presets():
    """Reload presets from file"""
    global rgp_preset_manager
    rgp_preset_manager.load_presets()

def register():
    """Register preset utilities"""
    print("RGP: Preset utilities registered")

def unregister():
    """Unregister preset utilities"""
    print("RGP: Preset utilities unregistered")