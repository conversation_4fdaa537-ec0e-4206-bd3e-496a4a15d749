"""
Rigaddon Play - UI Panels
Professional UI panels with tabs and controls
"""

import bpy
from bpy.types import Panel
from ..presets.utils_presets import get_preset_manager

class RGP_PT_MainPanel(Panel):
    """Main Rigaddon Play panel"""
    bl_label = "Rigaddon Play"
    bl_idname = "RGP_PT_main_panel"
    bl_space_type = 'VIEW_3D'
    bl_region_type = 'UI'
    bl_category = "Rigaddon"
    bl_context = "objectmode"

    def draw(self, context):
        layout = self.layout
        scene_props = context.scene.rgp_props

        # Header with logo/title
        header = layout.box()
        row = header.row(align=True)
        row.label(text="Rigaddon Play", icon='PLAY')
        row.operator("rgp.reload_presets", text="", icon='FILE_REFRESH')

        # Tab selection
        tab_row = layout.row(align=True)
        tab_row.prop(scene_props, "rgp_ui_tab", expand=True)

        # Content based on selected tab
        if scene_props.rgp_ui_tab == 'ANIMATIONS':
            self.draw_animations_tab(layout, context)
        elif scene_props.rgp_ui_tab == 'SETTINGS':
            self.draw_settings_tab(layout, context)



    def draw_animations_tab(self, layout, context):
        """Draw animations tab content"""
        scene_props = context.scene.rgp_props
        self.draw_animation_list_subtab(layout, context)

    def draw_animation_list_subtab(self, layout, context):
        """Draw animation list subtab content"""
        scene_props = context.scene.rgp_props

        # Animation list header
        header_box = layout.box()
        header_row = header_box.row(align=True)
        header_row.label(text="Animation List", icon='SEQUENCE')
        header_row.operator("rgp.clear_all_animations", text="", icon='TRASH')

        # Animation list with built-in add/remove buttons
        list_box = layout.box()

        # UIList with add/remove buttons
        row = list_box.row()

        # UIList
        col = row.column()
        col.template_list(
            "RGP_UL_AnimationList", "",
            scene_props, "rgp_animations",
            scene_props, "rgp_active_animation_index",
            rows=5
        )

        # Add/Remove buttons column
        col = row.column(align=True)
        col.operator("rgp.add_animation_popup", text="", icon='ADD')
        col.operator("rgp.remove_animation", text="", icon='REMOVE')
        col.separator()
        col.operator("rgp.move_animation", text="", icon='TRIA_UP').direction = 'UP'
        col.operator("rgp.move_animation", text="", icon='TRIA_DOWN').direction = 'DOWN'
        col.separator()
        col.operator("rgp.duplicate_animation", text="", icon='DUPLICATE')

        # Quick actions
        actions_box = layout.box()
        actions_box.label(text="Quick Actions", icon='PLAY')

        # Main playback controls
        actions_row1 = actions_box.row(align=True)
        actions_row1.operator("rgp.play_animation", text="Play", icon='PLAY')
        actions_row1.operator("rgp.stop_animation", text="Stop", icon='PAUSE')



        # Animation timing controls (if animation selected)
        if len(scene_props.rgp_animations) > 0 and scene_props.rgp_active_animation_index < len(scene_props.rgp_animations):
            active_anim = scene_props.rgp_animations[scene_props.rgp_active_animation_index]

            # Timing controls
            timing_box = layout.box()
            timing_box.label(text=f"Timing - {active_anim.rgp_name}", icon='TIME')

            timing_col = timing_box.column(align=True)
            timing_row = timing_col.row(align=True)
            timing_row.prop(active_anim, "rgp_start_frame", text="Start")
            timing_row.prop(active_anim, "rgp_end_frame", text="End")
            timing_col.prop(active_anim, "rgp_duration", text="Duration")

            # Basic properties
            basic_box = layout.box()
            basic_box.label(text="Basic Properties", icon='PROPERTIES')

            basic_col = basic_box.column(align=True)
            basic_col.prop(active_anim, "rgp_name", text="Name")
            basic_col.template_ID(active_anim, "rgp_target_object", text="Target Object")
            basic_col.prop(active_anim, "rgp_is_active", text="Active")

            # Easing controls
            easing_box = layout.box()
            easing_box.label(text="Easing Controls", icon='IPO_EASE_IN_OUT')

            easing_col = easing_box.column(align=True)
            easing_col.label(text="Global Easing Override:")

            # Add easing selection for the animation
            easing_row = easing_col.row(align=True)
            easing_row.operator("rgp.set_easing", text="Linear").easing_type = "LINEAR"
            easing_row.operator("rgp.set_easing", text="Ease In").easing_type = "EASE_IN"
            easing_row.operator("rgp.set_easing", text="Ease Out").easing_type = "EASE_OUT"

            easing_row2 = easing_col.row(align=True)
            easing_row2.operator("rgp.set_easing", text="Ease In/Out").easing_type = "EASE_IN_OUT"
            easing_row2.operator("rgp.set_easing", text="Bounce").easing_type = "BOUNCE"
            easing_row2.operator("rgp.set_easing", text="Elastic").easing_type = "ELASTIC"



    def draw_controls_tab(self, layout, context):
        """Draw controls tab content"""
        scene_props = context.scene.rgp_props

        # Global controls
        global_box = layout.box()
        global_box.label(text="Global Settings", icon='WORLD')

        global_col = global_box.column(align=True)
        global_col.prop(scene_props, "rgp_global_speed", text="Global Speed")
        global_col.prop(scene_props, "rgp_auto_preview", text="Auto Preview")
        global_col.prop(scene_props, "rgp_show_advanced", text="Show Advanced")

        # Info about dynamic panel
        layout.separator()
        info_box = layout.box()
        info_box.label(text="Animation Controls", icon='INFO')
        info_box.label(text="Select an animation from the Animations tab")
        info_box.label(text="to see detailed controls below.")

    def draw_settings_tab(self, layout, context):
        """Draw settings tab content"""
        scene_props = context.scene.rgp_props

        # Timeline settings
        timeline_box = layout.box()
        timeline_box.label(text="Timeline Settings", icon='TIME')

        timeline_col = timeline_box.column(align=True)
        timeline_row = timeline_col.row(align=True)
        timeline_row.prop(scene_props, "rgp_timeline_start", text="Start")
        timeline_row.prop(scene_props, "rgp_timeline_end", text="End")

        # Keyframe settings
        keyframe_box = layout.box()
        keyframe_box.label(text="Keyframe Settings", icon='KEYFRAME')

        keyframe_col = keyframe_box.column(align=True)
        keyframe_col.prop(scene_props, "rgp_backup_keyframes", text="Backup Existing Keyframes")
        keyframe_col.prop(scene_props, "rgp_clear_existing", text="Clear Existing Keyframes")

        # Transform controls
        transform_box = layout.box()
        transform_box.label(text="Transform Controls", icon='OBJECT_ORIGIN')

        transform_col = transform_box.column(align=True)
        transform_col.operator("rgp.reset_all_transforms", text="Reset All Transforms", icon='RECOVER_LAST')
        transform_col.label(text="Reset all objects to original position", icon='INFO')



    def draw_preset_properties(self, layout, animation_item, preset_data=None):
        """Draw preset-specific properties"""
        if preset_data is None:
            preset_manager = get_preset_manager()
            preset_data = preset_manager.get_preset(animation_item.rgp_preset_name)

        if not preset_data or 'properties' not in preset_data:
            return

        properties = preset_data['properties']

        for prop_name, prop_info in properties.items():
            prop_type = prop_info.get('type', 'float')
            prop_description = prop_info.get('description', '')

            if prop_type == 'float':
                # Use custom float properties
                if prop_name == 'height':
                    layout.prop(animation_item, "rgp_custom_float_1", text=prop_description)
                elif prop_name == 'damping':
                    layout.prop(animation_item, "rgp_custom_float_2", text=prop_description)
                elif prop_name == 'distance':
                    layout.prop(animation_item, "rgp_custom_float_1", text=prop_description)
            elif prop_type == 'int':
                if prop_name == 'bounces':
                    layout.prop(animation_item, "rgp_custom_int_1", text=prop_description)
                elif prop_name == 'duration':
                    layout.prop(animation_item, "rgp_custom_int_1", text=prop_description)
            elif prop_type == 'bool':
                layout.prop(animation_item, "rgp_custom_bool_1", text=prop_description)
            elif prop_type == 'enum':
                # For enum properties, we'd need a more sophisticated system
                layout.label(text=f"{prop_description}: {prop_info.get('default', 'N/A')}")



# Registration
classes = [
    RGP_PT_MainPanel,
]

def register():
    """Register UI panels"""
    from bpy.utils import register_class

    for cls in classes:
        register_class(cls)

    print("RGP: UI panels registered")

def unregister():
    """Unregister UI panels"""
    from bpy.utils import unregister_class

    for cls in reversed(classes):
        unregister_class(cls)

    print("RGP: UI panels unregistered")