"""
Rigaddon Play - Remove Animation Operators
Operators for removing and managing animations in the list
"""

import bpy
from bpy.types import Operator
from bpy.props import StringProperty, IntProperty, EnumProperty, BoolProperty
from ..utils.animation_core import get_animation_engine

class RGP_OT_RemoveAnimation(Operator):
    """Remove animation from the list"""
    bl_idname = "rgp.remove_animation"
    bl_label = "Remove Animation"
    bl_description = "Remove the selected animation from the list"
    bl_options = {'REGISTER', 'UNDO'}

    animation_index: IntProperty(
        name="Animation Index",
        description="Index of animation to remove",
        default=-1
    )

    remove_keyframes: BoolProperty(
        name="Remove Keyframes",
        description="Remove keyframes from the target object",
        default=True
    )

    restore_transform: BoolProperty(
        name="Restore Transform",
        description="Restore object to its original transform",
        default=True
    )

    def invoke(self, context, event):
        # Show confirmation dialog with options
        return context.window_manager.invoke_props_dialog(self)

    def draw(self, context):
        layout = self.layout
        layout.prop(self, "remove_keyframes")
        layout.prop(self, "restore_transform")

    def execute(self, context):
        try:
            scene_props = context.scene.rgp_props
            animation_engine = get_animation_engine()

            # Determine which animation to remove
            if self.animation_index >= 0:
                index = self.animation_index
            else:
                index = scene_props.rgp_active_animation_index

            # Validate index
            if index < 0 or index >= len(scene_props.rgp_animations):
                self.report({'ERROR'}, "Invalid animation index")
                return {'CANCELLED'}

            animation_item = scene_props.rgp_animations[index]
            animation_name = animation_item.rgp_name
            target_obj = animation_item.rgp_target_object
            target_obj_name = target_obj.name if target_obj else "Unknown"

            if target_obj and self.remove_keyframes:
                # Stop animation if it's currently playing
                if animation_item.rgp_is_playing:
                    bpy.ops.screen.animation_cancel()
                    animation_item.rgp_is_playing = False

                # Get frame range for this animation
                start_frame, end_frame = animation_engine.get_animation_frame_range(animation_item)

                # Remove keyframes
                success = animation_engine.remove_animation_keyframes(target_obj, start_frame, end_frame)

                if success and self.restore_transform and animation_item.rgp_has_backup:
                    # Restore original transform from backup
                    transform_backup = {
                        'location': animation_item.rgp_backup_location,
                        'rotation_euler': animation_item.rgp_backup_rotation,
                        'scale': animation_item.rgp_backup_scale
                    }
                    animation_engine.restore_object_transform(target_obj, transform_backup)
                    self.report({'INFO'}, f"Restored transform for '{target_obj_name}'")

                if success:
                    self.report({'INFO'}, f"Removed keyframes for '{animation_name}' from object '{target_obj_name}'")
                else:
                    self.report({'WARNING'}, f"Failed to remove keyframes for '{animation_name}'")

            # Remove animation from list
            scene_props.rgp_animations.remove(index)

            # Adjust active index
            if scene_props.rgp_active_animation_index >= len(scene_props.rgp_animations):
                scene_props.rgp_active_animation_index = max(0, len(scene_props.rgp_animations) - 1)

            self.report({'INFO'}, f"Removed animation '{animation_name}'")
            return {'FINISHED'}

        except Exception as e:
            self.report({'ERROR'}, f"Error removing animation: {str(e)}")
            return {'CANCELLED'}

class RGP_OT_ClearAllAnimations(Operator):
    """Clear all animations from the list"""
    bl_idname = "rgp.clear_all_animations"
    bl_label = "Clear All Animations"
    bl_description = "Remove all animations from the list"
    bl_options = {'REGISTER', 'UNDO'}

    def invoke(self, context, event):
        # Show confirmation dialog
        return context.window_manager.invoke_confirm(self, event)

    def execute(self, context):
        try:
            scene_props = context.scene.rgp_props

            count = len(scene_props.rgp_animations)
            scene_props.rgp_animations.clear()
            scene_props.rgp_active_animation_index = 0

            self.report({'INFO'}, f"Cleared {count} animations")
            return {'FINISHED'}

        except Exception as e:
            self.report({'ERROR'}, f"Error clearing animations: {str(e)}")
            return {'CANCELLED'}

class RGP_OT_DuplicateAnimation(Operator):
    """Duplicate the selected animation"""
    bl_idname = "rgp.duplicate_animation"
    bl_label = "Duplicate Animation"
    bl_description = "Create a copy of the selected animation"
    bl_options = {'REGISTER', 'UNDO'}

    animation_index: IntProperty(
        name="Animation Index",
        description="Index of animation to duplicate",
        default=-1
    )

    def execute(self, context):
        try:
            scene_props = context.scene.rgp_props

            # Determine which animation to duplicate
            if self.animation_index >= 0:
                index = self.animation_index
            else:
                index = scene_props.rgp_active_animation_index

            # Validate index
            if index < 0 or index >= len(scene_props.rgp_animations):
                self.report({'ERROR'}, "Invalid animation index")
                return {'CANCELLED'}

            source_anim = scene_props.rgp_animations[index]

            # Create duplicate
            new_anim = scene_props.rgp_animations.add()

            # Copy properties
            new_anim.rgp_name = f"{source_anim.rgp_name} Copy"
            new_anim.rgp_preset_name = source_anim.rgp_preset_name
            new_anim.rgp_target_object = source_anim.rgp_target_object
            new_anim.rgp_start_frame = source_anim.rgp_start_frame
            new_anim.rgp_end_frame = source_anim.rgp_end_frame
            new_anim.rgp_duration = source_anim.rgp_duration
            new_anim.rgp_is_active = source_anim.rgp_is_active
            new_anim.rgp_is_looping = source_anim.rgp_is_looping
            new_anim.rgp_loop_count = source_anim.rgp_loop_count

            # Copy custom properties
            new_anim.rgp_custom_float_1 = source_anim.rgp_custom_float_1
            new_anim.rgp_custom_float_2 = source_anim.rgp_custom_float_2
            new_anim.rgp_custom_float_3 = source_anim.rgp_custom_float_3
            new_anim.rgp_custom_int_1 = source_anim.rgp_custom_int_1
            new_anim.rgp_custom_int_2 = source_anim.rgp_custom_int_2
            new_anim.rgp_custom_string_1 = source_anim.rgp_custom_string_1
            new_anim.rgp_custom_bool_1 = source_anim.rgp_custom_bool_1
            new_anim.rgp_custom_bool_2 = source_anim.rgp_custom_bool_2

            # Copy keyframes
            for src_kf in source_anim.rgp_keyframes:
                new_kf = new_anim.rgp_keyframes.add()
                new_kf.rgp_frame = src_kf.rgp_frame
                new_kf.rgp_location = src_kf.rgp_location
                new_kf.rgp_rotation = src_kf.rgp_rotation
                new_kf.rgp_scale = src_kf.rgp_scale
                new_kf.rgp_alpha = src_kf.rgp_alpha
                new_kf.rgp_easing = src_kf.rgp_easing

            # Set as active animation
            scene_props.rgp_active_animation_index = len(scene_props.rgp_animations) - 1

            self.report({'INFO'}, f"Duplicated animation '{source_anim.rgp_name}'")
            return {'FINISHED'}

        except Exception as e:
            self.report({'ERROR'}, f"Error duplicating animation: {str(e)}")
            return {'CANCELLED'}

class RGP_OT_MoveAnimation(Operator):
    """Move animation up or down in the list"""
    bl_idname = "rgp.move_animation"
    bl_label = "Move Animation"
    bl_description = "Move the selected animation up or down in the list"
    bl_options = {'REGISTER', 'UNDO'}

    direction: EnumProperty(
        name="Direction",
        description="Direction to move the animation",
        items=[
            ('UP', 'Up', 'Move animation up'),
            ('DOWN', 'Down', 'Move animation down'),
        ],
        default='UP'
    )

    animation_index: IntProperty(
        name="Animation Index",
        description="Index of animation to move",
        default=-1
    )

    def execute(self, context):
        try:
            scene_props = context.scene.rgp_props

            # Determine which animation to move
            if self.animation_index >= 0:
                index = self.animation_index
            else:
                index = scene_props.rgp_active_animation_index

            # Validate index
            if index < 0 or index >= len(scene_props.rgp_animations):
                self.report({'ERROR'}, "Invalid animation index")
                return {'CANCELLED'}

            # Calculate new index
            if self.direction == 'UP':
                new_index = index - 1
            else:  # DOWN
                new_index = index + 1

            # Validate new index
            if new_index < 0 or new_index >= len(scene_props.rgp_animations):
                self.report({'WARNING'}, "Cannot move animation further in that direction")
                return {'CANCELLED'}

            # Move animation
            scene_props.rgp_animations.move(index, new_index)
            scene_props.rgp_active_animation_index = new_index

            direction_text = "up" if self.direction == 'UP' else "down"
            self.report({'INFO'}, f"Moved animation {direction_text}")
            return {'FINISHED'}

        except Exception as e:
            self.report({'ERROR'}, f"Error moving animation: {str(e)}")
            return {'CANCELLED'}

# Registration
classes = [
    RGP_OT_RemoveAnimation,
    RGP_OT_ClearAllAnimations,
    RGP_OT_DuplicateAnimation,
    RGP_OT_MoveAnimation,
]

def register():
    """Register remove animation operators"""
    from bpy.utils import register_class

    for cls in classes:
        register_class(cls)

    print("RGP: Remove animation operators registered")

def unregister():
    """Unregister remove animation operators"""
    from bpy.utils import unregister_class

    for cls in reversed(classes):
        unregister_class(cls)

    print("RGP: Remove animation operators unregistered")