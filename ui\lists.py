"""
Rigaddon Play - UI Lists
UIList classes for displaying animations and presets
"""

import bpy
from bpy.types import UIList

class RGP_UL_AnimationList(UIList):
    """UIList for displaying animation items"""

    def draw_item(self, context, layout, data, item, icon, active_data, active_propname, index):
        """Draw individual animation item in the list"""
        if self.layout_type in {'DEFAULT', 'COMPACT'}:
            # Animation name and status
            row = layout.row(align=True)

            # Active indicator
            if item.rgp_is_active:
                row.prop(item, "rgp_is_active", text="", icon='CHECKBOX_HLT', emboss=False)
            else:
                row.prop(item, "rgp_is_active", text="", icon='CHECKBOX_DEHLT', emboss=False)

            # Playing indicator
            if item.rgp_is_playing:
                row.label(text="", icon='PLAY')

            # Animation name
            row.prop(item, "rgp_name", text="", emboss=False)

            # Loop indicator
            if item.rgp_is_looping:
                row.label(text="", icon='FILE_REFRESH')

            # Target object with validation
            if item.rgp_target_object:
                target_obj = item.rgp_target_object
                # Check if object still exists in scene
                if target_obj and target_obj.name in bpy.data.objects:
                    row.label(text=target_obj.name, icon='OBJECT_DATA')
                else:
                    row.label(text="Missing Object", icon='ERROR')

            # Frame range
            row.label(text=f"{item.rgp_start_frame}-{item.rgp_end_frame}")

        elif self.layout_type == 'GRID':
            layout.alignment = 'CENTER'
            layout.label(text="", icon='SEQUENCE')

class RGP_UL_PresetList(UIList):
    """UIList for displaying preset items"""

    def draw_item(self, context, layout, data, item, icon, active_data, active_propname, index):
        """Draw individual preset item in the list"""
        if self.layout_type in {'DEFAULT', 'COMPACT'}:
            row = layout.row(align=True)

            # Preset icon based on category
            category_icons = {
                'Movement': 'ORIENTATION_CURSOR',
                'Entrance': 'IMPORT',
                'Exit': 'EXPORT',
                'Scale': 'FULLSCREEN',
                'Rotation': 'FILE_REFRESH',
                'Opacity': 'GHOST_ENABLED',
                'Custom': 'SETTINGS'
            }

            preset_icon = category_icons.get(item.get('category', 'Custom'), 'PRESET')
            row.label(text="", icon=preset_icon)

            # Preset name
            row.label(text=item.get('name', 'Unknown Preset'))

            # Category
            row.label(text=item.get('category', 'Custom'))

        elif self.layout_type == 'GRID':
            layout.alignment = 'CENTER'
            layout.label(text="", icon='PRESET')

class RGP_UL_KeyframeList(UIList):
    """UIList for displaying keyframes of an animation"""

    def draw_item(self, context, layout, data, item, icon, active_data, active_propname, index):
        """Draw individual keyframe item in the list"""
        if self.layout_type in {'DEFAULT', 'COMPACT'}:
            row = layout.row(align=True)

            # Frame number
            row.label(text=f"Frame {item.rgp_frame}", icon='KEYFRAME')

            # Easing type
            easing_icons = {
                'LINEAR': 'IPO_LINEAR',
                'EASE_IN': 'IPO_EASE_IN',
                'EASE_OUT': 'IPO_EASE_OUT',
                'EASE_IN_OUT': 'IPO_EASE_IN_OUT',
                'BOUNCE': 'IPO_BOUNCE',
                'ELASTIC': 'IPO_ELASTIC',
                'BACK': 'IPO_BACK'
            }

            easing_icon = easing_icons.get(item.rgp_easing, 'IPO_LINEAR')
            row.label(text=item.rgp_easing, icon=easing_icon)

        elif self.layout_type == 'GRID':
            layout.alignment = 'CENTER'
            layout.label(text="", icon='KEYFRAME')

class RGP_UL_PropertyList(UIList):
    """UIList for displaying custom properties"""

    def draw_item(self, context, layout, data, item, icon, active_data, active_propname, index):
        """Draw individual property item in the list"""
        if self.layout_type in {'DEFAULT', 'COMPACT'}:
            row = layout.row(align=True)

            # Property type icon
            type_icons = {
                'FLOAT': 'DRIVER',
                'INT': 'PLUS',
                'BOOL': 'CHECKBOX_HLT',
                'STRING': 'FONT_DATA',
                'ENUM': 'DOWNARROW_HLT'
            }

            type_icon = type_icons.get(item.rgp_prop_type, 'DRIVER')
            row.label(text="", icon=type_icon)

            # Property name
            row.prop(item, "rgp_prop_name", text="", emboss=False)

            # Property value based on type
            if item.rgp_prop_type == 'FLOAT':
                row.prop(item, "rgp_float_value", text="")
            elif item.rgp_prop_type == 'INT':
                row.prop(item, "rgp_int_value", text="")
            elif item.rgp_prop_type == 'BOOL':
                row.prop(item, "rgp_bool_value", text="")
            elif item.rgp_prop_type == 'STRING':
                row.prop(item, "rgp_string_value", text="")
            elif item.rgp_prop_type == 'ENUM':
                row.prop(item, "rgp_enum_value", text="")

        elif self.layout_type == 'GRID':
            layout.alignment = 'CENTER'
            layout.label(text="", icon='PROPERTIES')

# Registration
classes = [
    RGP_UL_AnimationList,
    RGP_UL_PresetList,
    RGP_UL_KeyframeList,
    RGP_UL_PropertyList,
]

def register():
    """Register UI lists"""
    from bpy.utils import register_class

    for cls in classes:
        register_class(cls)

    print("RGP: UI lists registered")

def unregister():
    """Unregister UI lists"""
    from bpy.utils import unregister_class

    for cls in reversed(classes):
        unregister_class(cls)

    print("RGP: UI lists unregistered")