# Rigaddon Play - Professional Animation Addon for Blender 4.3

**Created by: <PERSON><PERSON><PERSON>**  
**Category: Rigaddon**  
**Version: 1.0.0**

## Overview

Rigaddon Play adalah addon animasi profesional untuk Blender 4.3 yang menyediakan sistem preset animasi yang mudah digunakan, kontrol playback yang canggih, dan antarmuka yang user-friendly. Addon ini dirancang untuk mempercepat workflow animasi dengan menyediakan preset animasi siap pakai dan tools manajemen animasi yang powerful.

## Features

### 🎯 **Preset-Based Animation System**
- **Berbagai Kategori Preset**: Movement, Entrance, Exit, Scale, Rotation, Opacity, Custom
- **Preset Siap Pakai**: Bounce, Slide In/Out, Fade In/Out, Scale Bounce, Rotate 360°, Wobble, Elastic Bounce, Spiral In, dan banyak lagi
- **Custom Preset Creation**: Buat preset animasi kustom Anda sendiri
- **JSON-Based Configuration**: Mudah untuk dikustomisasi dan diperluas

### 🎮 **Professional UI System**
- **Tab-Based Interface**: Presets, Animations, Controls, Settings
- **UIList for Animation Management**: Kelola daftar animasi dengan mudah
- **Real-time Preview**: Preview animasi sebelum apply
- **Professional Layout**: UI yang bersih dan intuitif

### 🎬 **Advanced Animation Controls**
- **Multiple Playback Modes**: Single, All, From Selected, Loop Selected
- **Loop Control**: Set jumlah loop atau infinite loop
- **Timeline Management**: Kontrol start/end frame otomatis
- **Global Speed Control**: Adjust kecepatan animasi secara global

### 🔧 **Advanced Features**
- **Animation Baking**: Convert animasi ke keyframe
- **Import/Export**: Simpan dan load animasi dalam format JSON
- **Batch Operations**: Operasi pada multiple animasi sekaligus
- **Backup System**: Backup keyframe existing sebelum apply animasi baru
- **Context Menus**: Quick access melalui right-click menu

## Installation

1. Download addon sebagai ZIP file
2. Buka Blender 4.3
3. Go to Edit > Preferences > Add-ons
4. Click "Install..." dan pilih ZIP file
5. Enable "Rigaddon Play" addon
6. Addon akan muncul di 3D Viewport sidebar (N-panel) di kategori "Rigaddon"

## Quick Start

### 1. **Applying Animation Presets**
1. Pilih object yang ingin dianimasi
2. Buka Rigaddon Play panel di sidebar (N-panel)
3. Pilih tab "Presets"
4. Pilih kategori animasi
5. Click preset yang diinginkan
6. Animasi akan diterapkan dan ditambahkan ke animation list

### 2. **Managing Animations**
1. Switch ke tab "Animations"
2. Lihat daftar animasi yang sudah ditambahkan
3. Use controls untuk play, duplicate, remove, atau reorder animasi
4. Set active/inactive status untuk setiap animasi

### 3. **Controlling Playback**
1. Switch ke tab "Controls"
2. Pilih animasi dari list
3. Adjust timing, loop settings, dan custom properties
4. Use playback controls untuk play/stop/preview

## Preset Categories

### **Movement**
- **Bounce**: Animasi bouncing dengan customizable height dan damping
- **Wobble**: Gerakan wobble dengan rotasi dan movement
- **Elastic Bounce**: Bounce dengan efek elastic

### **Entrance**
- **Slide In Left**: Object masuk dari kiri
- **Fade In**: Fade dari transparent ke opaque
- **Spiral In**: Object spiral masuk sambil scaling

### **Exit**
- **Slide Out Right**: Object keluar ke kanan
- **Fade Out**: Fade dari opaque ke transparent

### **Scale**
- **Scale Bounce**: Scale up dengan bouncy effect
- **Scale Pulse**: Pulsing scale animation

### **Rotation**
- **Rotate 360°**: Rotasi 360 derajat dengan customizable axis dan direction

## Advanced Usage

### **Creating Custom Presets**
1. Click "Create Custom Preset" di tab Presets
2. Enter nama dan deskripsi
3. Preset akan dibuat dengan struktur dasar
4. Edit file JSON untuk customization lebih lanjut

### **Import/Export Animations**
1. Go to tab Settings
2. Use "Export Animations" untuk save current animation list
3. Use "Import Animations" untuk load animation list dari file

### **Animation Baking**
1. Pilih animasi yang ingin di-bake
2. Go to advanced controls
3. Click "Bake Animation" untuk convert ke actual keyframes

## Technical Details

### **File Structure**
```
Rigaddon Play/
├── __init__.py              # Main addon file
├── properties/              # Property definitions
├── operators/               # Operator implementations
├── ui/                      # UI panels and lists
├── utils/                   # Core animation engine
├── presets/                 # Preset management
├── menus/                   # Context menus
└── README.md               # Documentation
```

### **Naming Convention**
- Semua class, variable, dan operator menggunakan prefix `RGP_` (Rigaddon Play)
- Properties menggunakan prefix `rgp_`
- Operator IDs menggunakan format `rgp.operation_name`

## Troubleshooting

### **Preset Not Loading**
- Check console untuk error messages
- Verify JSON file format di presets/anim_presets.json
- Use "Reload Presets" button

### **Animation Not Playing**
- Check apakah object masih exist di scene
- Verify animation is marked as active
- Check timeline range settings

### **Performance Issues**
- Reduce jumlah active animations
- Use "Clear Existing Keyframes" option
- Bake animations yang sudah final

## Support & Documentation

- **Documentation**: Click "Documentation" button di Settings tab
- **Issues**: Report bugs melalui addon preferences
- **Updates**: Check untuk update di Blender addon manager

## License

Copyright © 2024 Rigel Tapangan. All rights reserved.

---

**Rigaddon Play** - Making animation workflow faster and more enjoyable! 🎬✨
