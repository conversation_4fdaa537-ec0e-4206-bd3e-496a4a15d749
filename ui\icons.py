"""
Rigaddon Play - UI Icons
Icon management and utilities for the UI
"""

import bpy
import os
from typing import Dict, Optional

class RGP_IconManager:
    """Manager for custom icons and icon utilities"""

    def __init__(self):
        self.custom_icons = {}
        self.icon_previews = None

    def initialize_icons(self):
        """Initialize custom icon previews"""
        try:
            import bpy.utils.previews
            self.icon_previews = bpy.utils.previews.new()

            # Load custom icons if they exist
            addon_dir = os.path.dirname(os.path.dirname(__file__))
            icons_dir = os.path.join(addon_dir, "icons")

            if os.path.exists(icons_dir):
                self.load_custom_icons(icons_dir)

            print("RGP: Icons initialized")

        except Exception as e:
            print(f"RGP: Error initializing icons: {e}")

    def load_custom_icons(self, icons_dir: str):
        """Load custom icons from directory"""
        try:
            for filename in os.listdir(icons_dir):
                if filename.lower().endswith(('.png', '.jpg', '.jpeg')):
                    icon_name = os.path.splitext(filename)[0]
                    icon_path = os.path.join(icons_dir, filename)

                    self.icon_previews.load(icon_name, icon_path, 'IMAGE')
                    self.custom_icons[icon_name] = self.icon_previews[icon_name].icon_id

            print(f"RGP: Loaded {len(self.custom_icons)} custom icons")

        except Exception as e:
            print(f"RGP: Error loading custom icons: {e}")

    def get_icon(self, icon_name: str) -> int:
        """Get icon ID for custom icon"""
        return self.custom_icons.get(icon_name, 0)

    def cleanup_icons(self):
        """Cleanup icon previews"""
        if self.icon_previews:
            bpy.utils.previews.remove(self.icon_previews)
            self.icon_previews = None
            self.custom_icons.clear()
            print("RGP: Icons cleaned up")

# Icon mappings for different categories and types
CATEGORY_ICONS = {
    'Movement': 'ORIENTATION_CURSOR',
    'Entrance': 'IMPORT',
    'Exit': 'EXPORT',
    'Scale': 'FULLSCREEN',
    'Rotation': 'FILE_REFRESH',
    'Opacity': 'GHOST_ENABLED',
    'Custom': 'SETTINGS'
}

EASING_ICONS = {
    'LINEAR': 'IPO_LINEAR',
    'EASE_IN': 'IPO_EASE_IN',
    'EASE_OUT': 'IPO_EASE_OUT',
    'EASE_IN_OUT': 'IPO_EASE_IN_OUT',
    'BOUNCE': 'IPO_BOUNCE',
    'ELASTIC': 'IPO_ELASTIC',
    'BACK': 'IPO_BACK'
}

PROPERTY_TYPE_ICONS = {
    'FLOAT': 'DRIVER',
    'INT': 'PLUS',
    'BOOL': 'CHECKBOX_HLT',
    'STRING': 'FONT_DATA',
    'ENUM': 'DOWNARROW_HLT'
}

PLAYBACK_ICONS = {
    'PLAY': 'PLAY',
    'PAUSE': 'PAUSE',
    'STOP': 'SNAP_FACE',
    'LOOP': 'FILE_REFRESH',
    'FORWARD': 'NEXT_KEYFRAME',
    'BACKWARD': 'PREV_KEYFRAME'
}

def get_category_icon(category: str) -> str:
    """Get icon for animation category"""
    return CATEGORY_ICONS.get(category, 'PRESET')

def get_easing_icon(easing: str) -> str:
    """Get icon for easing type"""
    return EASING_ICONS.get(easing, 'IPO_LINEAR')

def get_property_type_icon(prop_type: str) -> str:
    """Get icon for property type"""
    return PROPERTY_TYPE_ICONS.get(prop_type, 'DRIVER')

def get_playback_icon(action: str) -> str:
    """Get icon for playback action"""
    return PLAYBACK_ICONS.get(action, 'PLAY')

# Global icon manager instance
rgp_icon_manager = RGP_IconManager()

def get_icon_manager() -> RGP_IconManager:
    """Get the global icon manager instance"""
    return rgp_icon_manager

def register():
    """Register icon manager"""
    rgp_icon_manager.initialize_icons()
    print("RGP: Icon manager registered")

def unregister():
    """Unregister icon manager"""
    rgp_icon_manager.cleanup_icons()
    print("RGP: Icon manager unregistered")