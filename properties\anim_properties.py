"""
Rigaddon Play - Animation Properties
Properties for storing animation data and settings
"""

import bpy
from bpy.props import (
    StringProperty, IntProperty, Float<PERSON>roperty, BoolProperty,
    EnumProperty, CollectionProperty, PointerProperty, FloatVectorProperty
)
from bpy.types import PropertyGroup

def rgp_poll_target_object_RGP(self, object):
    """Poll function to filter target objects - only show objects in current scene"""
    try:
        # Only show objects that exist in the current scene
        if object and object.name in bpy.data.objects:
            # Check if object is in the current scene
            current_scene = bpy.context.scene
            return object.name in current_scene.objects
        return False
    except:
        return False

def rgp_update_start_frame(self, context):
    """Update animation keyframes when start frame changes"""
    try:
        if not hasattr(self, '_updating_timing'):
            self._updating_timing = True
            from ..utils.animation_core import get_animation_engine

            engine = get_animation_engine()
            if engine and self.rgp_target_object:
                # Calculate new end frame based on duration
                new_end_frame = self.rgp_start_frame + self.rgp_duration - 1
                self.rgp_end_frame = new_end_frame

                # Find the animation index for this item
                scene_props = context.scene.rgp_props
                animation_index = -1
                for i, anim in enumerate(scene_props.rgp_animations):
                    if anim == self:
                        animation_index = i
                        break

                # Update keyframes for this specific animation only
                if animation_index >= 0:
                    engine.update_animation_timing(
                        animation_index,
                        self.rgp_start_frame,
                        self.rgp_end_frame,
                        self.rgp_duration
                    )

            del self._updating_timing
    except Exception as e:
        print(f"RGP: Error updating start frame: {e}")
        if hasattr(self, '_updating_timing'):
            del self._updating_timing

def rgp_update_end_frame(self, context):
    """Update animation keyframes when end frame changes"""
    try:
        if not hasattr(self, '_updating_timing'):
            self._updating_timing = True
            from ..utils.animation_core import get_animation_engine

            engine = get_animation_engine()
            if engine and self.rgp_target_object:
                # Calculate new duration
                new_duration = self.rgp_end_frame - self.rgp_start_frame + 1
                self.rgp_duration = max(1, new_duration)

                # Find the animation index for this item
                scene_props = context.scene.rgp_props
                animation_index = -1
                for i, anim in enumerate(scene_props.rgp_animations):
                    if anim == self:
                        animation_index = i
                        break

                # Update keyframes for this specific animation only
                if animation_index >= 0:
                    engine.update_animation_timing(
                        animation_index,
                        self.rgp_start_frame,
                        self.rgp_end_frame,
                        self.rgp_duration
                    )

            del self._updating_timing
    except Exception as e:
        print(f"RGP: Error updating end frame: {e}")
        if hasattr(self, '_updating_timing'):
            del self._updating_timing

def rgp_update_duration(self, context):
    """Update animation keyframes when duration changes"""
    try:
        if not hasattr(self, '_updating_timing'):
            self._updating_timing = True
            from ..utils.animation_core import get_animation_engine

            engine = get_animation_engine()
            if engine and self.rgp_target_object:
                # Calculate new end frame
                new_end_frame = self.rgp_start_frame + self.rgp_duration - 1
                self.rgp_end_frame = new_end_frame

                # Find the animation index for this item
                scene_props = context.scene.rgp_props
                animation_index = -1
                for i, anim in enumerate(scene_props.rgp_animations):
                    if anim == self:
                        animation_index = i
                        break

                # Update keyframes for this specific animation only
                if animation_index >= 0:
                    engine.update_animation_timing(
                        animation_index,
                        self.rgp_start_frame,
                        self.rgp_end_frame,
                        self.rgp_duration
                    )

            del self._updating_timing
    except Exception as e:
        print(f"RGP: Error updating duration: {e}")
        if hasattr(self, '_updating_timing'):
            del self._updating_timing

class RGP_AnimationKeyframe(PropertyGroup):
    """Individual keyframe data"""

    rgp_frame: IntProperty(
        name="Frame",
        description="Frame number for this keyframe",
        default=1,
        min=1
    )

    rgp_location: FloatVectorProperty(
        name="Location",
        description="Location values for this keyframe",
        size=3,
        default=(0.0, 0.0, 0.0)
    )

    rgp_rotation: FloatVectorProperty(
        name="Rotation",
        description="Rotation values for this keyframe (Euler)",
        size=3,
        default=(0.0, 0.0, 0.0)
    )

    rgp_scale: FloatVectorProperty(
        name="Scale",
        description="Scale values for this keyframe",
        size=3,
        default=(1.0, 1.0, 1.0)
    )

    rgp_alpha: FloatProperty(
        name="Alpha",
        description="Alpha/opacity value for this keyframe",
        default=1.0,
        min=0.0,
        max=1.0
    )

    rgp_easing: EnumProperty(
        name="Easing",
        description="Interpolation type for this keyframe",
        items=[
            ('LINEAR', 'Linear', 'Linear interpolation'),
            ('EASE_IN', 'Ease In', 'Ease in'),
            ('EASE_OUT', 'Ease Out', 'Ease out'),
            ('EASE_IN_OUT', 'Ease In/Out', 'Ease in and out'),
            ('BOUNCE', 'Bounce', 'Bounce effect'),
            ('ELASTIC', 'Elastic', 'Elastic effect'),
            ('BACK', 'Back', 'Back effect'),
        ],
        default='EASE_IN_OUT'
    )

class RGP_AnimationItem(PropertyGroup):
    """Individual animation item in the list"""

    rgp_name: StringProperty(
        name="Animation Name",
        description="Name of this animation",
        default="New Animation"
    )

    rgp_preset_name: StringProperty(
        name="Preset Name",
        description="Name of the preset used for this animation",
        default=""
    )

    rgp_target_object: PointerProperty(
        name="Target Object",
        description="Target object for this animation",
        type=bpy.types.Object,
        poll=rgp_poll_target_object_RGP
    )

    rgp_start_frame: IntProperty(
        name="Start Frame",
        description="Starting frame for this animation",
        default=1,
        min=1,
        update=rgp_update_start_frame
    )

    rgp_end_frame: IntProperty(
        name="End Frame",
        description="Ending frame for this animation",
        default=30,
        min=1,
        update=rgp_update_end_frame
    )

    rgp_duration: IntProperty(
        name="Duration",
        description="Duration of animation in frames",
        default=30,
        min=1,
        update=rgp_update_duration
    )

    rgp_is_active: BoolProperty(
        name="Active",
        description="Whether this animation is currently active",
        default=True
    )

    rgp_is_playing: BoolProperty(
        name="Playing",
        description="Whether this animation is currently playing",
        default=False
    )

    rgp_play_priority: IntProperty(
        name="Play Priority",
        description="Priority for animation playback (higher = more priority)",
        default=0,
        min=0,
        max=100
    )

    rgp_is_looping: BoolProperty(
        name="Loop",
        description="Whether this animation should loop",
        default=False
    )

    rgp_loop_count: IntProperty(
        name="Loop Count",
        description="Number of times to loop (-1 for infinite)",
        default=1,
        min=-1
    )

    rgp_keyframes: CollectionProperty(
        type=RGP_AnimationKeyframe,
        name="Keyframes"
    )

    # Custom properties for preset parameters
    rgp_custom_float_1: FloatProperty(name="Custom Float 1", default=0.0)
    rgp_custom_float_2: FloatProperty(name="Custom Float 2", default=0.0)
    rgp_custom_float_3: FloatProperty(name="Custom Float 3", default=0.0)
    rgp_custom_int_1: IntProperty(name="Custom Int 1", default=0)
    rgp_custom_int_2: IntProperty(name="Custom Int 2", default=0)
    rgp_custom_string_1: StringProperty(name="Custom String 1", default="")
    rgp_custom_bool_1: BoolProperty(name="Custom Bool 1", default=False)
    rgp_custom_bool_2: BoolProperty(name="Custom Bool 2", default=False)

    # Transform backup properties
    rgp_backup_location: FloatVectorProperty(
        name="Backup Location",
        description="Backup of original object location",
        size=3,
        default=(0.0, 0.0, 0.0)
    )

    rgp_backup_rotation: FloatVectorProperty(
        name="Backup Rotation",
        description="Backup of original object rotation",
        size=3,
        default=(0.0, 0.0, 0.0)
    )

    rgp_backup_scale: FloatVectorProperty(
        name="Backup Scale",
        description="Backup of original object scale",
        size=3,
        default=(1.0, 1.0, 1.0)
    )

    rgp_has_backup: BoolProperty(
        name="Has Backup",
        description="Whether this animation has a transform backup",
        default=False
    )

class RGP_SceneProperties(PropertyGroup):
    """Main scene properties for Rigaddon Play"""

    # Animation list
    rgp_animations: CollectionProperty(
        type=RGP_AnimationItem,
        name="Animations"
    )

    rgp_active_animation_index: IntProperty(
        name="Active Animation Index",
        description="Index of currently selected animation",
        default=0,
        min=0
    )

    # UI settings
    rgp_ui_tab: EnumProperty(
        name="UI Tab",
        description="Currently active UI tab",
        items=[
            ('ANIMATIONS', 'Animations', 'Animation list'),
            ('SETTINGS', 'Settings', 'Addon settings'),
        ],
        default='ANIMATIONS'
    )



    rgp_preset_category: EnumProperty(
        name="Preset Category",
        description="Filter presets by category",
        items=[
            ('ALL', 'All', 'Show all presets'),
            ('Movement', 'Movement', 'Movement animations'),
            ('Entrance', 'Entrance', 'Entrance animations'),
            ('Exit', 'Exit', 'Exit animations'),
            ('Scale', 'Scale', 'Scale animations'),
            ('Rotation', 'Rotation', 'Rotation animations'),
            ('Opacity', 'Opacity', 'Opacity animations'),
            ('Custom', 'Custom', 'Custom animations'),
        ],
        default='ALL'
    )

    # Playback settings
    rgp_playback_mode: EnumProperty(
        name="Playback Mode",
        description="Animation playback mode",
        items=[
            ('SINGLE', 'Single', 'Play single animation'),
            ('ALL', 'All', 'Play all animations'),
            ('FROM_SELECTED', 'From Selected', 'Play from selected animation'),
            ('LOOP_SELECTED', 'Loop Selected', 'Loop selected animation'),
        ],
        default='SINGLE'
    )

    rgp_auto_preview: BoolProperty(
        name="Auto Preview",
        description="Automatically preview animations when selected",
        default=False
    )

    rgp_global_speed: FloatProperty(
        name="Global Speed",
        description="Global animation speed multiplier",
        default=1.0,
        min=0.1,
        max=5.0
    )

    # Timeline settings
    rgp_timeline_start: IntProperty(
        name="Timeline Start",
        description="Timeline start frame",
        default=1,
        min=1
    )

    rgp_timeline_end: IntProperty(
        name="Timeline End",
        description="Timeline end frame",
        default=250,
        min=1
    )

    # Advanced settings
    rgp_show_advanced: BoolProperty(
        name="Show Advanced",
        description="Show advanced animation options",
        default=False
    )

    rgp_backup_keyframes: BoolProperty(
        name="Backup Keyframes",
        description="Backup existing keyframes before applying animations",
        default=True
    )

    rgp_clear_existing: BoolProperty(
        name="Clear Existing",
        description="Clear existing keyframes before applying new animation",
        default=False
    )

# Registration
classes = [
    RGP_AnimationKeyframe,
    RGP_AnimationItem,
    RGP_SceneProperties,
]

def register():
    """Register animation properties"""
    from bpy.utils import register_class

    for cls in classes:
        register_class(cls)

    # Add properties to scene
    bpy.types.Scene.rgp_props = PointerProperty(type=RGP_SceneProperties)

    print("RGP: Animation properties registered")

def unregister():
    """Unregister animation properties"""
    from bpy.utils import unregister_class

    # Remove properties from scene
    if hasattr(bpy.types.Scene, 'rgp_props'):
        del bpy.types.Scene.rgp_props

    for cls in reversed(classes):
        unregister_class(cls)

    print("RGP: Animation properties unregistered")