{"animation_presets": {"Custom Animation": {"name": "Custom Animation", "description": "", "category": "Custom", "icon": "SETTINGS", "properties": {}, "keyframes": [{"frame": 1, "location": [0, 0, 0], "easing": "EASE_IN_OUT"}, {"frame": 30, "location": [0, 0, 0], "easing": "EASE_IN_OUT"}]}, "crystallize": {"name": "Crystallize", "description": "Object crystallizes with angular movements", "category": "Entrance", "icon": "MESH_ICOSPHERE", "properties": {"segments": {"type": "int", "default": 6, "min": 3, "max": 12, "description": "Number of crystal segments"}}, "keyframes": [{"frame": 1, "scale": [0, 0, 0], "rotation_euler": [0, 0, 0], "easing": "LINEAR"}, {"frame": 10, "scale": [0.5, 0.5, 0.5], "rotation_euler": [0.5, 0.3, 0.2], "easing": "LINEAR"}, {"frame": 20, "scale": [0.8, 0.8, 0.8], "rotation_euler": [-0.3, 0.7, -0.4], "easing": "LINEAR"}, {"frame": 30, "scale": [1, 1, 1], "rotation_euler": [0, 0, 0], "easing": "EASE_IN"}]}, "fade_in": {"name": "Fade In", "description": "Object fades in from transparent to opaque", "category": "Entrance", "icon": "GHOST_ENABLED", "properties": {"duration": {"type": "int", "default": 30, "min": 5, "max": 120, "description": "Animation duration in frames"}}, "keyframes": [{"frame": 1, "alpha": 0.0, "easing": "LINEAR"}, {"frame": "duration", "alpha": 1.0, "easing": "EASE_IN_OUT"}]}, "grow": {"name": "Grow", "description": "Object grows from nothing to full size", "category": "Entrance", "icon": "PLUS", "properties": {"overshoot": {"type": "float", "default": 0.2, "min": 0.0, "max": 0.5, "description": "Growth overshoot"}}, "keyframes": [{"frame": 1, "scale": [0, 0, 0], "easing": "EASE_OUT"}, {"frame": 20, "scale": ["1+overshoot", "1+overshoot", "1+overshoot"], "easing": "EASE_IN_OUT"}, {"frame": 30, "scale": [1, 1, 1], "easing": "EASE_IN"}]}, "hologram": {"name": "Hologram", "description": "Object appears as a flickering hologram", "category": "Entrance", "icon": "GHOST_ENABLED", "properties": {"flicker_speed": {"type": "float", "default": 1.0, "min": 0.5, "max": 3.0, "description": "Hologram flicker speed"}}, "keyframes": [{"frame": 1, "alpha": 0.0, "easing": "LINEAR"}, {"frame": 3, "alpha": 0.7, "easing": "LINEAR"}, {"frame": 5, "alpha": 0.2, "easing": "LINEAR"}, {"frame": 8, "alpha": 0.9, "easing": "LINEAR"}, {"frame": 10, "alpha": 0.4, "easing": "LINEAR"}, {"frame": 13, "alpha": 0.8, "easing": "LINEAR"}, {"frame": 15, "alpha": 0.3, "easing": "LINEAR"}, {"frame": 18, "alpha": 1.0, "easing": "LINEAR"}, {"frame": 20, "alpha": 0.6, "easing": "LINEAR"}, {"frame": 25, "alpha": 0.9, "easing": "LINEAR"}, {"frame": 30, "alpha": 1.0, "easing": "LINEAR"}]}, "matrix": {"name": "Matrix", "description": "Object appears with digital matrix effect", "category": "Entrance", "icon": "CONSOLE", "properties": {"digital_intensity": {"type": "float", "default": 1.0, "min": 0.5, "max": 2.0, "description": "Digital effect intensity"}}, "keyframes": [{"frame": 1, "alpha": 0.0, "scale": [1, 0, 1], "easing": "LINEAR"}, {"frame": 10, "alpha": 0.5, "scale": [1, 0.3, 1], "easing": "LINEAR"}, {"frame": 20, "alpha": 0.8, "scale": [1, 0.7, 1], "easing": "LINEAR"}, {"frame": 30, "alpha": 1.0, "scale": [1, 1, 1], "easing": "EASE_IN"}]}, "roll_in": {"name": "Roll In", "description": "Object rolls in from the side while rotating", "category": "Entrance", "icon": "MESH_CIRCLE", "properties": {"distance": {"type": "float", "default": 8.0, "min": 2.0, "max": 20.0, "description": "Roll distance"}, "rotations": {"type": "float", "default": 2.0, "min": 0.5, "max": 5.0, "description": "Number of rotations"}}, "keyframes": [{"frame": 1, "location": ["-distance", 0, 0], "rotation_euler": [0, 0, 0], "easing": "EASE_OUT"}, {"frame": 30, "location": [0, 0, 0], "rotation_euler": [0, 0, "6.28318*rotations"], "easing": "EASE_IN_OUT"}]}, "slide_in_left": {"name": "Slide In Left", "description": "Object slides in from the left side", "category": "Entrance", "icon": "TRIA_RIGHT", "properties": {"distance": {"type": "float", "default": 5.0, "min": 0.1, "max": 20.0, "description": "Slide distance"}, "speed": {"type": "float", "default": 1.0, "min": 0.1, "max": 5.0, "description": "Animation speed multiplier"}}, "keyframes": [{"frame": 1, "location": ["-distance", 0, 0], "easing": "EASE_OUT"}, {"frame": 30, "location": [0, 0, 0], "easing": "EASE_IN_OUT"}]}, "slide_up": {"name": "Slide Up", "description": "Object slides up from below", "category": "Entrance", "icon": "TRIA_UP", "properties": {"distance": {"type": "float", "default": 5.0, "min": 1.0, "max": 20.0, "description": "Slide distance"}}, "keyframes": [{"frame": 1, "location": [0, 0, "-distance"], "easing": "EASE_OUT"}, {"frame": 30, "location": [0, 0, 0], "easing": "EASE_IN_OUT"}]}, "spiral_in": {"name": "Spiral In", "description": "Object spirals inward while scaling down", "category": "Entrance", "icon": "FORCE_VORTEX", "properties": {"radius": {"type": "float", "default": 5.0, "min": 1.0, "max": 15.0, "description": "Spiral radius"}, "turns": {"type": "float", "default": 2.0, "min": 0.5, "max": 5.0, "description": "Number of spiral turns"}}, "keyframes": [{"frame": 1, "location": ["radius", 0, 0], "scale": [0.1, 0.1, 0.1], "rotation_euler": [0, 0, 0], "easing": "EASE_OUT"}, {"frame": 15, "location": ["radius*0.7", "radius*0.7", 0], "scale": [0.4, 0.4, 0.4], "rotation_euler": [0, 0, "1.57*turns"], "easing": "EASE_IN_OUT"}, {"frame": 30, "location": ["radius*0.3", "radius*0.3", 0], "scale": [0.7, 0.7, 0.7], "rotation_euler": [0, 0, "3.14*turns"], "easing": "EASE_IN_OUT"}, {"frame": 45, "location": ["radius*0.1", "radius*0.1", 0], "scale": [0.9, 0.9, 0.9], "rotation_euler": [0, 0, "4.71*turns"], "easing": "EASE_IN_OUT"}, {"frame": 60, "location": [0, 0, 0], "scale": [1, 1, 1], "rotation_euler": [0, 0, "6.28*turns"], "easing": "EASE_IN"}]}, "spiral_in_scale": {"name": "Spiral In Scale", "description": "Object spirals inward while scaling up", "category": "Entrance", "icon": "FORCE_VORTEX", "properties": {"rotations": {"type": "float", "default": 3.0, "min": 1.0, "max": 10.0, "description": "Number of rotations"}, "start_scale": {"type": "float", "default": 0.1, "min": 0.01, "max": 0.5, "description": "Starting scale"}}, "keyframes": [{"frame": 1, "scale": ["start_scale", "start_scale", "start_scale"], "rotation_euler": [0, 0, "6.28*rotations"], "easing": "EASE_OUT"}, {"frame": 30, "scale": [1, 1, 1], "rotation_euler": [0, 0, 0], "easing": "EASE_IN_OUT"}]}, "teleport_in": {"name": "Teleport In", "description": "Object teleports in with flash effect", "category": "Entrance", "icon": "LIGHT_SUN", "properties": {"flash_intensity": {"type": "float", "default": 2.0, "min": 1.0, "max": 5.0, "description": "Flash intensity"}}, "keyframes": [{"frame": 1, "scale": [0, 0, 0], "alpha": 0.0, "easing": "LINEAR"}, {"frame": 5, "scale": ["flash_intensity", "flash_intensity", "flash_intensity"], "alpha": 1.0, "easing": "EASE_OUT"}, {"frame": 15, "scale": [1, 1, 1], "alpha": 1.0, "easing": "EASE_IN"}]}, "typewriter": {"name": "Typewriter", "description": "<PERSON><PERSON> appears character by character", "category": "Entrance", "icon": "FONT_DATA", "properties": {"speed": {"type": "float", "default": 1.0, "min": 0.5, "max": 3.0, "description": "Typing speed"}}, "keyframes": [{"frame": 1, "scale": [0, 1, 1], "easing": "LINEAR"}, {"frame": 30, "scale": [1, 1, 1], "easing": "LINEAR"}]}, "zoom_in": {"name": "Zoom In", "description": "Object zooms in from small to normal size", "category": "Entrance", "icon": "ZOOM_IN", "properties": {"start_scale": {"type": "float", "default": 0.1, "min": 0.01, "max": 0.5, "description": "Starting scale"}, "overshoot": {"type": "float", "default": 0.1, "min": 0.0, "max": 0.5, "description": "Overshoot amount"}}, "keyframes": [{"frame": 1, "scale": ["start_scale", "start_scale", "start_scale"], "easing": "EASE_OUT"}, {"frame": 20, "scale": ["1+overshoot", "1+overshoot", "1+overshoot"], "easing": "EASE_IN_OUT"}, {"frame": 30, "scale": [1, 1, 1], "easing": "EASE_IN"}]}, "explode": {"name": "Explode", "description": "Object explodes outward with scaling", "category": "Exit", "icon": "FORCE_FORCE", "properties": {"scale_factor": {"type": "float", "default": 3.0, "min": 2.0, "max": 10.0, "description": "Explosion scale factor"}}, "keyframes": [{"frame": 1, "scale": [1, 1, 1], "alpha": 1.0, "easing": "EASE_OUT"}, {"frame": 15, "scale": ["scale_factor", "scale_factor", "scale_factor"], "alpha": 0.0, "easing": "EASE_IN"}]}, "fade_out": {"name": "Fade Out", "description": "Object fades out from opaque to transparent", "category": "Exit", "icon": "GHOST_DISABLED", "properties": {"duration": {"type": "int", "default": 30, "min": 5, "max": 120, "description": "Animation duration in frames"}}, "keyframes": [{"frame": 1, "alpha": 1.0, "easing": "LINEAR"}, {"frame": "duration", "alpha": 0.0, "easing": "EASE_IN_OUT"}]}, "implode": {"name": "Implode", "description": "Object implodes inward with scaling", "category": "Exit", "icon": "FORCE_FORCE", "properties": {"scale_factor": {"type": "float", "default": 0.1, "min": 0.01, "max": 0.5, "description": "Implosion scale factor"}}, "keyframes": [{"frame": 1, "scale": [1, 1, 1], "alpha": 1.0, "easing": "EASE_OUT"}, {"frame": 15, "scale": ["scale_factor", "scale_factor", "scale_factor"], "alpha": 0.0, "easing": "EASE_IN"}]}, "melt": {"name": "<PERSON><PERSON>", "description": "Object melts downward", "category": "Exit", "icon": "MOD_FLUID", "properties": {"melt_factor": {"type": "float", "default": 2.0, "min": 1.5, "max": 5.0, "description": "Melt factor"}}, "keyframes": [{"frame": 1, "scale": [1, 1, 1], "location": [0, 0, 0], "easing": "EASE_OUT"}, {"frame": 20, "scale": ["melt_factor", 0.5, 1], "location": [0, 0, -1], "easing": "EASE_IN_OUT"}, {"frame": 30, "scale": ["melt_factor*1.5", 0.1, 1], "location": [0, 0, -2], "alpha": 0.0, "easing": "EASE_IN"}]}, "shrink": {"name": "Shrink", "description": "Object shrinks from full size to nothing", "category": "Exit", "icon": "REMOVE", "properties": {"overshoot": {"type": "float", "default": 0.2, "min": 0.0, "max": 0.5, "description": "Shrink overshoot"}}, "keyframes": [{"frame": 1, "scale": [1, 1, 1], "easing": "EASE_OUT"}, {"frame": 10, "scale": ["1+overshoot", "1+overshoot", "1+overshoot"], "easing": "EASE_IN_OUT"}, {"frame": 30, "scale": [0, 0, 0], "easing": "EASE_IN"}]}, "slide_down": {"name": "Slide Down", "description": "Object slides down and exits", "category": "Exit", "icon": "TRIA_DOWN", "properties": {"distance": {"type": "float", "default": 5.0, "min": 1.0, "max": 20.0, "description": "Slide distance"}}, "keyframes": [{"frame": 1, "location": [0, 0, 0], "easing": "EASE_IN"}, {"frame": 30, "location": [0, 0, "-distance"], "easing": "EASE_OUT"}]}, "slide_out_right": {"name": "Slide Out Right", "description": "Object slides out to the right side", "category": "Exit", "icon": "TRIA_RIGHT", "properties": {"distance": {"type": "float", "default": 5.0, "min": 0.1, "max": 20.0, "description": "Slide distance"}, "speed": {"type": "float", "default": 1.0, "min": 0.1, "max": 5.0, "description": "Animation speed multiplier"}}, "keyframes": [{"frame": 1, "location": [0, 0, 0], "easing": "EASE_IN"}, {"frame": 30, "location": ["distance", 0, 0], "easing": "EASE_OUT"}]}, "spiral_out": {"name": "Spiral Out", "description": "Object spirals outward while scaling down", "category": "Exit", "icon": "FORCE_VORTEX", "properties": {"rotations": {"type": "float", "default": 3.0, "min": 1.0, "max": 10.0, "description": "Number of rotations"}, "end_scale": {"type": "float", "default": 0.1, "min": 0.01, "max": 0.5, "description": "Ending scale"}}, "keyframes": [{"frame": 1, "scale": [1, 1, 1], "rotation_euler": [0, 0, 0], "easing": "EASE_OUT"}, {"frame": 30, "scale": ["end_scale", "end_scale", "end_scale"], "rotation_euler": [0, 0, "6.28*rotations"], "easing": "EASE_IN_OUT"}]}, "teleport_out": {"name": "Teleport Out", "description": "Object teleports out with flash effect", "category": "Exit", "icon": "LIGHT_SUN", "properties": {"flash_intensity": {"type": "float", "default": 2.0, "min": 1.0, "max": 5.0, "description": "Flash intensity"}}, "keyframes": [{"frame": 1, "scale": [1, 1, 1], "alpha": 1.0, "easing": "EASE_OUT"}, {"frame": 10, "scale": ["flash_intensity", "flash_intensity", "flash_intensity"], "alpha": 1.0, "easing": "EASE_IN_OUT"}, {"frame": 15, "scale": [0, 0, 0], "alpha": 0.0, "easing": "LINEAR"}]}, "zoom_out": {"name": "Zoom Out", "description": "Object zooms out from normal to small size", "category": "Exit", "icon": "ZOOM_OUT", "properties": {"end_scale": {"type": "float", "default": 0.1, "min": 0.01, "max": 0.5, "description": "Ending scale"}, "overshoot": {"type": "float", "default": 0.1, "min": 0.0, "max": 0.5, "description": "Overshoot amount"}}, "keyframes": [{"frame": 1, "scale": [1, 1, 1], "easing": "EASE_OUT"}, {"frame": 10, "scale": ["1+overshoot", "1+overshoot", "1+overshoot"], "easing": "EASE_IN_OUT"}, {"frame": 30, "scale": ["end_scale", "end_scale", "end_scale"], "easing": "EASE_IN"}]}, "elastic_bounce": {"name": "Elastic Bounce", "description": "Object bounces with elastic effect", "category": "Movement", "icon": "FORCE_SPRING", "properties": {"height": {"type": "float", "default": 3.0, "min": 0.5, "max": 10.0, "description": "Bounce height"}, "elasticity": {"type": "float", "default": 0.7, "min": 0.1, "max": 1.0, "description": "Elastic factor"}}, "keyframes": [{"frame": 1, "location": [0, 0, 0], "easing": "EASE_OUT"}, {"frame": 12, "location": [0, 0, "height"], "easing": "ELASTIC"}, {"frame": 24, "location": [0, 0, 0], "easing": "BOUNCE"}, {"frame": 32, "location": [0, 0, "height*elasticity"], "easing": "ELASTIC"}, {"frame": 40, "location": [0, 0, 0], "easing": "EASE_IN"}]}, "glitch": {"name": "Glitch", "description": "Object glitches with random movements", "category": "Movement", "icon": "FORCE_TURBULENCE", "properties": {"intensity": {"type": "float", "default": 0.5, "min": 0.1, "max": 2.0, "description": "Glitch intensity"}}, "keyframes": [{"frame": 1, "location": [0, 0, 0], "easing": "LINEAR"}, {"frame": 3, "location": ["intensity*0.8", "intensity*0.3", 0], "easing": "LINEAR"}, {"frame": 5, "location": ["-intensity*0.5", "intensity*0.7", 0], "easing": "LINEAR"}, {"frame": 7, "location": ["intensity*0.2", "-intensity*0.9", 0], "easing": "LINEAR"}, {"frame": 9, "location": ["-intensity*0.6", "-intensity*0.2", 0], "easing": "LINEAR"}, {"frame": 11, "location": ["intensity*0.9", "intensity*0.1", 0], "easing": "LINEAR"}, {"frame": 13, "location": ["-intensity*0.1", "intensity*0.8", 0], "easing": "LINEAR"}, {"frame": 15, "location": [0, 0, 0], "easing": "LINEAR"}]}, "gravity": {"name": "Gravity", "description": "Object falls under gravity influence", "category": "Movement", "icon": "FORCE_FORCE", "properties": {"fall_distance": {"type": "float", "default": 5.0, "min": 2.0, "max": 15.0, "description": "Fall distance"}, "bounce_factor": {"type": "float", "default": 0.6, "min": 0.2, "max": 0.9, "description": "Bounce factor"}}, "keyframes": [{"frame": 1, "location": [0, 0, "fall_distance"], "easing": "EASE_IN"}, {"frame": 20, "location": [0, 0, 0], "easing": "EASE_OUT"}, {"frame": 30, "location": [0, 0, "fall_distance*bounce_factor"], "easing": "EASE_IN"}, {"frame": 40, "location": [0, 0, 0], "easing": "EASE_OUT"}, {"frame": 45, "location": [0, 0, "fall_distance*bounce_factor*0.5"], "easing": "EASE_IN"}, {"frame": 50, "location": [0, 0, 0], "easing": "EASE_IN"}]}, "levitate": {"name": "Levitate", "description": "Object gently floats up and down", "category": "Movement", "icon": "FORCE_WIND", "properties": {"height": {"type": "float", "default": 1.0, "min": 0.5, "max": 3.0, "description": "Levitation height"}}, "keyframes": [{"frame": 1, "location": [0, 0, 0], "easing": "EASE_IN_OUT"}, {"frame": 30, "location": [0, 0, "height"], "easing": "EASE_IN_OUT"}, {"frame": 60, "location": [0, 0, 0], "easing": "EASE_IN_OUT"}]}, "magnetic": {"name": "Magnetic", "description": "Object is attracted by magnetic force", "category": "Movement", "icon": "FORCE_MAGNETIC", "properties": {"pull_distance": {"type": "float", "default": 3.0, "min": 1.0, "max": 10.0, "description": "Magnetic pull distance"}}, "keyframes": [{"frame": 1, "location": ["-pull_distance", 0, 0], "easing": "EASE_OUT"}, {"frame": 15, "location": ["-pull_distance*0.3", 0, 0], "easing": "EASE_IN_OUT"}, {"frame": 25, "location": ["pull_distance*0.1", 0, 0], "easing": "EASE_IN_OUT"}, {"frame": 30, "location": [0, 0, 0], "easing": "EASE_IN"}]}, "orbit": {"name": "Orbit", "description": "Object orbits around its original position", "category": "Movement", "icon": "MESH_CIRCLE", "properties": {"radius": {"type": "float", "default": 3.0, "min": 1.0, "max": 10.0, "description": "Orbit radius"}, "speed": {"type": "float", "default": 1.0, "min": 0.5, "max": 3.0, "description": "Orbit speed"}}, "keyframes": [{"frame": 1, "location": ["radius", 0, 0], "easing": "LINEAR"}, {"frame": 15, "location": [0, "radius", 0], "easing": "LINEAR"}, {"frame": 30, "location": ["-radius", 0, 0], "easing": "LINEAR"}, {"frame": 45, "location": [0, "-radius", 0], "easing": "LINEAR"}, {"frame": 60, "location": ["radius", 0, 0], "easing": "LINEAR"}]}, "shake": {"name": "Shake", "description": "Object shakes with random movement", "category": "Movement", "icon": "FORCE_TURBULENCE", "properties": {"intensity": {"type": "float", "default": 0.5, "min": 0.1, "max": 2.0, "description": "Shake intensity"}, "frequency": {"type": "int", "default": 8, "min": 4, "max": 20, "description": "Shake frequency"}}, "keyframes": [{"frame": 1, "location": [0, 0, 0], "easing": "LINEAR"}, {"frame": 4, "location": ["intensity*0.3", "intensity*0.2", 0], "easing": "LINEAR"}, {"frame": 7, "location": ["-intensity*0.4", "intensity*0.3", 0], "easing": "LINEAR"}, {"frame": 10, "location": ["intensity*0.2", "-intensity*0.4", 0], "easing": "LINEAR"}, {"frame": 13, "location": ["-intensity*0.3", "-intensity*0.2", 0], "easing": "LINEAR"}, {"frame": 16, "location": ["intensity*0.4", "intensity*0.1", 0], "easing": "LINEAR"}, {"frame": 19, "location": ["-intensity*0.1", "intensity*0.4", 0], "easing": "LINEAR"}, {"frame": 22, "location": ["intensity*0.2", "-intensity*0.3", 0], "easing": "LINEAR"}, {"frame": 25, "location": ["-intensity*0.2", "intensity*0.2", 0], "easing": "LINEAR"}, {"frame": 28, "location": ["intensity*0.1", "-intensity*0.1", 0], "easing": "LINEAR"}, {"frame": 30, "location": [0, 0, 0], "easing": "EASE_OUT"}]}, "swing": {"name": "Swing", "description": "Object swings like a pendulum", "category": "Movement", "icon": "FORCE_CURVE", "properties": {"angle": {"type": "float", "default": 30.0, "min": 10.0, "max": 90.0, "description": "Swing angle in degrees"}, "swings": {"type": "int", "default": 3, "min": 1, "max": 10, "description": "Number of swings"}}, "keyframes": [{"frame": 1, "rotation_euler": [0, 0, 0], "easing": "EASE_OUT"}, {"frame": 15, "rotation_euler": [0, 0, "angle*0.0174533"], "easing": "EASE_IN_OUT"}, {"frame": 30, "rotation_euler": [0, 0, "-angle*0.0174533"], "easing": "EASE_IN_OUT"}, {"frame": 45, "rotation_euler": [0, 0, "angle*0.0174533*0.7"], "easing": "EASE_IN_OUT"}, {"frame": 60, "rotation_euler": [0, 0, 0], "easing": "EASE_IN"}]}, "wave": {"name": "Wave", "description": "Object moves in a wave pattern", "category": "Movement", "icon": "MOD_WAVE", "properties": {"amplitude": {"type": "float", "default": 2.0, "min": 0.5, "max": 5.0, "description": "Wave amplitude"}, "frequency": {"type": "float", "default": 2.0, "min": 0.5, "max": 5.0, "description": "Wave frequency"}}, "keyframes": [{"frame": 1, "location": [0, 0, 0], "easing": "LINEAR"}, {"frame": 8, "location": [0, "amplitude", 0], "easing": "LINEAR"}, {"frame": 15, "location": [0, 0, 0], "easing": "LINEAR"}, {"frame": 22, "location": [0, "-amplitude", 0], "easing": "LINEAR"}, {"frame": 30, "location": [0, 0, 0], "easing": "LINEAR"}]}, "wobble": {"name": "Wobble", "description": "Object wobbles with rotation and slight movement", "category": "Movement", "icon": "FORCE_HARMONIC", "properties": {"intensity": {"type": "float", "default": 0.2, "min": 0.1, "max": 1.0, "description": "Wobble intensity"}, "frequency": {"type": "int", "default": 4, "min": 2, "max": 10, "description": "Wobble frequency"}}, "keyframes": [{"frame": 1, "rotation_euler": [0, 0, 0], "location": [0, 0, 0], "easing": "EASE_IN_OUT"}, {"frame": 8, "rotation_euler": ["intensity", 0, "intensity*0.5"], "location": ["intensity*0.1", 0, 0], "easing": "EASE_IN_OUT"}, {"frame": 15, "rotation_euler": ["-intensity", 0, "-intensity*0.5"], "location": ["-intensity*0.1", 0, 0], "easing": "EASE_IN_OUT"}, {"frame": 23, "rotation_euler": ["intensity*0.5", 0, "intensity*0.3"], "location": ["intensity*0.05", 0, 0], "easing": "EASE_IN_OUT"}, {"frame": 30, "rotation_euler": [0, 0, 0], "location": [0, 0, 0], "easing": "EASE_OUT"}]}, "wobble_decay": {"name": "Wob<PERSON>", "description": "Object wobbles with decreasing intensity", "category": "Movement", "icon": "FORCE_CURVE", "properties": {"intensity": {"type": "float", "default": 1.0, "min": 0.1, "max": 3.0, "description": "Wobble intensity"}}, "keyframes": [{"frame": 1, "rotation_euler": [0, 0, 0], "easing": "EASE_OUT"}, {"frame": 5, "rotation_euler": [0, 0, "intensity*0.5"], "easing": "EASE_IN_OUT"}, {"frame": 10, "rotation_euler": [0, 0, "-intensity*0.4"], "easing": "EASE_IN_OUT"}, {"frame": 15, "rotation_euler": [0, 0, "intensity*0.3"], "easing": "EASE_IN_OUT"}, {"frame": 20, "rotation_euler": [0, 0, "-intensity*0.2"], "easing": "EASE_IN_OUT"}, {"frame": 25, "rotation_euler": [0, 0, "intensity*0.1"], "easing": "EASE_IN_OUT"}, {"frame": 30, "rotation_euler": [0, 0, 0], "easing": "EASE_IN"}]}, "flash": {"name": "Flash", "description": "Object flashes by quickly changing opacity", "category": "Opacity", "icon": "LIGHT_SUN", "properties": {"flashes": {"type": "int", "default": 3, "min": 1, "max": 10, "description": "Number of flashes"}, "flash_speed": {"type": "float", "default": 0.5, "min": 0.1, "max": 2.0, "description": "Flash speed multiplier"}}, "keyframes": [{"frame": 1, "alpha": 1.0, "easing": "LINEAR"}, {"frame": 5, "alpha": 0.0, "easing": "LINEAR"}, {"frame": 10, "alpha": 1.0, "easing": "LINEAR"}, {"frame": 15, "alpha": 0.0, "easing": "LINEAR"}, {"frame": 20, "alpha": 1.0, "easing": "LINEAR"}, {"frame": 25, "alpha": 0.0, "easing": "LINEAR"}, {"frame": 30, "alpha": 1.0, "easing": "LINEAR"}]}, "quantum": {"name": "Quantum", "description": "Object phases in and out of existence", "category": "Opacity", "icon": "GHOST_ENABLED", "properties": {"phases": {"type": "int", "default": 4, "min": 2, "max": 8, "description": "Number of quantum phases"}}, "keyframes": [{"frame": 1, "alpha": 1.0, "scale": [1, 1, 1], "easing": "LINEAR"}, {"frame": 5, "alpha": 0.2, "scale": [0.8, 0.8, 0.8], "easing": "LINEAR"}, {"frame": 10, "alpha": 1.0, "scale": [1.2, 1.2, 1.2], "easing": "LINEAR"}, {"frame": 15, "alpha": 0.1, "scale": [0.6, 0.6, 0.6], "easing": "LINEAR"}, {"frame": 20, "alpha": 1.0, "scale": [1.1, 1.1, 1.1], "easing": "LINEAR"}, {"frame": 25, "alpha": 0.3, "scale": [0.9, 0.9, 0.9], "easing": "LINEAR"}, {"frame": 30, "alpha": 1.0, "scale": [1, 1, 1], "easing": "LINEAR"}]}, "flip_horizontal": {"name": "<PERSON><PERSON>", "description": "Object flips horizontally around Y-axis", "category": "Rotation", "icon": "MOD_MIRROR", "properties": {"speed": {"type": "float", "default": 1.0, "min": 0.5, "max": 3.0, "description": "Flip speed"}, "flips": {"type": "int", "default": 1, "min": 1, "max": 5, "description": "Number of flips"}}, "keyframes": [{"frame": 1, "rotation_euler": [0, 0, 0], "easing": "EASE_OUT"}, {"frame": 15, "rotation_euler": [0, "3.14159*flips", 0], "easing": "EASE_IN_OUT"}, {"frame": 30, "rotation_euler": [0, "6.28318*flips", 0], "easing": "EASE_IN"}]}, "flip_vertical": {"name": "Flip Vertical", "description": "Object flips vertically around X-axis", "category": "Rotation", "icon": "MOD_MIRROR", "properties": {"flips": {"type": "int", "default": 1, "min": 1, "max": 5, "description": "Number of flips"}}, "keyframes": [{"frame": 1, "rotation_euler": [0, 0, 0], "easing": "EASE_OUT"}, {"frame": 15, "rotation_euler": ["3.14159*flips", 0, 0], "easing": "EASE_IN_OUT"}, {"frame": 30, "rotation_euler": ["6.28318*flips", 0, 0], "easing": "EASE_IN"}]}, "rotate_360": {"name": "Rotate 360°", "description": "Complete 360 degree rotation around Z-axis", "category": "Rotation", "icon": "FILE_REFRESH", "properties": {"axis": {"type": "enum", "default": "Z", "items": [["X", "X-Axis", ""], ["Y", "Y-Axis", ""], ["Z", "Z-Axis", ""]], "description": "Rotation axis"}, "direction": {"type": "enum", "default": "CW", "items": [["CW", "Clockwise", ""], ["CCW", "Counter-Clockwise", ""]], "description": "Rotation direction"}, "speed": {"type": "float", "default": 1.0, "min": 0.1, "max": 5.0, "description": "Rotation speed"}}, "keyframes": [{"frame": 1, "rotation_euler": [0, 0, 0], "easing": "LINEAR"}, {"frame": 60, "rotation_euler": [0, 0, "6.28318*direction_multiplier"], "easing": "LINEAR"}]}, "twist": {"name": "Twist", "description": "Object twists around Z-axis", "category": "Rotation", "icon": "MOD_SCREW", "properties": {"angle": {"type": "float", "default": 180.0, "min": 45.0, "max": 720.0, "description": "Twist angle in degrees"}}, "keyframes": [{"frame": 1, "rotation_euler": [0, 0, 0], "easing": "EASE_OUT"}, {"frame": 15, "rotation_euler": [0, 0, "angle*0.0174533*0.5"], "easing": "EASE_IN_OUT"}, {"frame": 30, "rotation_euler": [0, 0, "angle*0.0174533"], "easing": "EASE_IN"}]}, "vortex": {"name": "Vortex", "description": "Object spins in a vortex pattern", "category": "Rotation", "icon": "FORCE_VORTEX", "properties": {"intensity": {"type": "float", "default": 2.0, "min": 1.0, "max": 5.0, "description": "Vortex intensity"}}, "keyframes": [{"frame": 1, "rotation_euler": [0, 0, 0], "scale": [1, 1, 1], "easing": "EASE_OUT"}, {"frame": 15, "rotation_euler": ["intensity*2", "intensity*1.5", "intensity*3"], "scale": [0.8, 0.8, 0.8], "easing": "EASE_IN_OUT"}, {"frame": 30, "rotation_euler": ["intensity*4", "intensity*3", "intensity*6"], "scale": [1, 1, 1], "easing": "EASE_IN"}]}, "breathe": {"name": "Breathe", "description": "Object breathes with gentle scaling", "category": "Scale", "icon": "HEART", "properties": {"intensity": {"type": "float", "default": 0.1, "min": 0.05, "max": 0.3, "description": "Breathing intensity"}}, "keyframes": [{"frame": 1, "scale": [1, 1, 1], "easing": "EASE_IN_OUT"}, {"frame": 30, "scale": ["1+intensity", "1+intensity", "1+intensity"], "easing": "EASE_IN_OUT"}, {"frame": 60, "scale": [1, 1, 1], "easing": "EASE_IN_OUT"}]}, "heartbeat": {"name": "Heartbeat", "description": "Object pulses like a heartbeat", "category": "Scale", "icon": "HEART", "properties": {"intensity": {"type": "float", "default": 0.2, "min": 0.1, "max": 0.5, "description": "Heartbeat intensity"}, "beats": {"type": "int", "default": 2, "min": 1, "max": 5, "description": "Number of beats"}}, "keyframes": [{"frame": 1, "scale": [1, 1, 1], "easing": "EASE_OUT"}, {"frame": 8, "scale": ["1+intensity", "1+intensity", "1+intensity"], "easing": "EASE_IN_OUT"}, {"frame": 12, "scale": [1, 1, 1], "easing": "EASE_OUT"}, {"frame": 18, "scale": ["1+intensity*0.8", "1+intensity*0.8", "1+intensity*0.8"], "easing": "EASE_IN_OUT"}, {"frame": 22, "scale": [1, 1, 1], "easing": "EASE_IN"}, {"frame": 40, "scale": [1, 1, 1], "easing": "LINEAR"}]}, "jello": {"name": "<PERSON><PERSON>", "description": "Object jiggles like jello", "category": "Scale", "icon": "FORCE_SPRING", "properties": {"intensity": {"type": "float", "default": 0.3, "min": 0.1, "max": 1.0, "description": "Jiggle intensity"}}, "keyframes": [{"frame": 1, "scale": [1, 1, 1], "easing": "EASE_OUT"}, {"frame": 8, "scale": ["1+intensity", "1-intensity*0.5", 1], "easing": "EASE_IN_OUT"}, {"frame": 16, "scale": ["1-intensity*0.5", "1+intensity", 1], "easing": "EASE_IN_OUT"}, {"frame": 24, "scale": ["1+intensity*0.3", "1-intensity*0.3", 1], "easing": "EASE_IN_OUT"}, {"frame": 30, "scale": [1, 1, 1], "easing": "EASE_IN"}]}, "morph": {"name": "Morph", "description": "Object morphs through different scales", "category": "Scale", "icon": "MOD_SMOOTH", "properties": {"morph_factor": {"type": "float", "default": 1.5, "min": 1.1, "max": 3.0, "description": "Morph factor"}}, "keyframes": [{"frame": 1, "scale": [1, 1, 1], "easing": "EASE_OUT"}, {"frame": 10, "scale": ["morph_factor", 1, 1], "easing": "EASE_IN_OUT"}, {"frame": 20, "scale": [1, "morph_factor", 1], "easing": "EASE_IN_OUT"}, {"frame": 30, "scale": [1, 1, "morph_factor"], "easing": "EASE_IN_OUT"}, {"frame": 40, "scale": [1, 1, 1], "easing": "EASE_IN"}]}, "rubber_band": {"name": "Rubber Band", "description": "Object stretches and snaps back like rubber band", "category": "Scale", "icon": "FORCE_SPRING", "properties": {"stretch_x": {"type": "float", "default": 1.5, "min": 1.1, "max": 3.0, "description": "X-axis stretch factor"}, "stretch_y": {"type": "float", "default": 0.7, "min": 0.3, "max": 0.9, "description": "Y-axis compression factor"}}, "keyframes": [{"frame": 1, "scale": [1, 1, 1], "easing": "EASE_OUT"}, {"frame": 15, "scale": ["stretch_x", "stretch_y", 1], "easing": "EASE_IN_OUT"}, {"frame": 25, "scale": [0.9, 1.1, 1], "easing": "ELASTIC"}, {"frame": 35, "scale": [1.05, 0.95, 1], "easing": "EASE_IN_OUT"}, {"frame": 45, "scale": [1, 1, 1], "easing": "EASE_IN"}]}, "scale_bounce": {"name": "Scale Bounce", "description": "Object scales up with a bouncy effect", "category": "Scale", "icon": "FULLSCREEN_ENTER", "properties": {"scale_factor": {"type": "float", "default": 1.5, "min": 0.1, "max": 5.0, "description": "Maximum scale factor"}, "overshoot": {"type": "float", "default": 0.2, "min": 0.0, "max": 1.0, "description": "Overshoot amount"}}, "keyframes": [{"frame": 1, "scale": [0.1, 0.1, 0.1], "easing": "EASE_OUT"}, {"frame": 15, "scale": ["scale_factor+overshoot", "scale_factor+overshoot", "scale_factor+overshoot"], "easing": "EASE_IN_OUT"}, {"frame": 25, "scale": ["scale_factor", "scale_factor", "scale_factor"], "easing": "EASE_IN_OUT"}]}, "scale_pulse": {"name": "Scale Pulse", "description": "Object pulses by scaling up and down", "category": "Scale", "icon": "RADIOBUT_ON", "properties": {"scale_factor": {"type": "float", "default": 1.2, "min": 0.1, "max": 3.0, "description": "Maximum scale factor"}, "pulses": {"type": "int", "default": 2, "min": 1, "max": 10, "description": "Number of pulses"}}, "keyframes": [{"frame": 1, "scale": [1, 1, 1], "easing": "EASE_OUT"}, {"frame": 15, "scale": ["scale_factor", "scale_factor", "scale_factor"], "easing": "EASE_IN_OUT"}, {"frame": 30, "scale": [1, 1, 1], "easing": "EASE_IN"}, {"frame": 45, "scale": ["scale_factor", "scale_factor", "scale_factor"], "easing": "EASE_IN_OUT"}, {"frame": 60, "scale": [1, 1, 1], "easing": "EASE_OUT"}]}}}