"""
Rigaddon Play - Animation Core Engine
Core animation system for applying presets and managing keyframes
"""

import bpy
import bmesh
import mathutils
from mathutils import Vector, <PERSON><PERSON><PERSON>
from typing import Dict, List, Any, Optional, Tuple
import re

class RGP_AnimationEngine:
    """Core animation engine for Rigaddon Play"""

    def __init__(self):
        self.current_frame = 1
        self.backup_data = {}

    def evaluate_expression(self, expression: str, variables: Dict[str, Any]) -> Any:
        """Safely evaluate mathematical expressions with variables"""
        if not isinstance(expression, str):
            return expression

        # Replace variable names with their values
        result = expression
        for var_name, var_value in variables.items():
            if var_name in result:
                result = result.replace(var_name, str(var_value))

        # Handle special multipliers
        if "direction_multiplier" in result:
            # This would be set based on direction enum
            result = result.replace("direction_multiplier", "1")

        try:
            # Safe evaluation of mathematical expressions
            # Only allow basic math operations
            allowed_chars = set("0123456789+-*/.() ")
            if all(c in allowed_chars for c in result):
                return eval(result)
            else:
                return float(result) if result.replace('.', '').replace('-', '').isdigit() else result
        except:
            return expression

    def backup_object_keyframes(self, obj: bpy.types.Object) -> Dict[str, Any]:
        """Backup existing keyframes for an object"""
        backup = {
            'location': [],
            'rotation_euler': [],
            'scale': [],
            'material_alpha': []
        }

        if obj.animation_data and obj.animation_data.action:
            action = obj.animation_data.action

            for fcurve in action.fcurves:
                data_path = fcurve.data_path
                array_index = fcurve.array_index

                keyframe_data = []
                for keyframe in fcurve.keyframe_points:
                    keyframe_data.append({
                        'frame': keyframe.co[0],
                        'value': keyframe.co[1],
                        'interpolation': keyframe.interpolation,
                        'easing': keyframe.easing
                    })

                if data_path == 'location':
                    if len(backup['location']) <= array_index:
                        backup['location'].extend([[] for _ in range(array_index + 1 - len(backup['location']))])
                    backup['location'][array_index] = keyframe_data
                elif data_path == 'rotation_euler':
                    if len(backup['rotation_euler']) <= array_index:
                        backup['rotation_euler'].extend([[] for _ in range(array_index + 1 - len(backup['rotation_euler']))])
                    backup['rotation_euler'][array_index] = keyframe_data
                elif data_path == 'scale':
                    if len(backup['scale']) <= array_index:
                        backup['scale'].extend([[] for _ in range(array_index + 1 - len(backup['scale']))])
                    backup['scale'][array_index] = keyframe_data

        return backup

    def restore_object_keyframes(self, obj: bpy.types.Object, backup: Dict[str, Any]):
        """Restore backed up keyframes"""
        if not obj.animation_data:
            obj.animation_data_create()

        if not obj.animation_data.action:
            obj.animation_data.action = bpy.data.actions.new(name=f"{obj.name}_Action")

        action = obj.animation_data.action

        # Clear existing keyframes
        action.fcurves.clear()

        # Restore location keyframes
        for i, axis_data in enumerate(backup.get('location', [])):
            if axis_data:
                fcurve = action.fcurves.new(data_path='location', index=i)
                for kf_data in axis_data:
                    kf = fcurve.keyframe_points.insert(kf_data['frame'], kf_data['value'])
                    kf.interpolation = kf_data['interpolation']
                    kf.easing = kf_data['easing']

        # Restore rotation keyframes
        for i, axis_data in enumerate(backup.get('rotation_euler', [])):
            if axis_data:
                fcurve = action.fcurves.new(data_path='rotation_euler', index=i)
                for kf_data in axis_data:
                    kf = fcurve.keyframe_points.insert(kf_data['frame'], kf_data['value'])
                    kf.interpolation = kf_data['interpolation']
                    kf.easing = kf_data['easing']

        # Restore scale keyframes
        for i, axis_data in enumerate(backup.get('scale', [])):
            if axis_data:
                fcurve = action.fcurves.new(data_path='scale', index=i)
                for kf_data in axis_data:
                    kf = fcurve.keyframe_points.insert(kf_data['frame'], kf_data['value'])
                    kf.interpolation = kf_data['interpolation']
                    kf.easing = kf_data['easing']

    def clear_object_keyframes(self, obj: bpy.types.Object, properties: List[str] = None):
        """Clear keyframes for specified properties"""
        if not obj.animation_data or not obj.animation_data.action:
            return

        action = obj.animation_data.action
        if properties is None:
            properties = ['location', 'rotation_euler', 'scale']

        fcurves_to_remove = []
        for fcurve in action.fcurves:
            if fcurve.data_path in properties:
                fcurves_to_remove.append(fcurve)

        for fcurve in fcurves_to_remove:
            action.fcurves.remove(fcurve)

    def apply_preset_to_object(self, obj: bpy.types.Object, preset_data: Dict[str, Any],
                              custom_properties: Dict[str, Any] = None,
                              start_frame: int = 1) -> bool:
        """Apply animation preset to an object"""
        try:
            if not preset_data or 'keyframes' not in preset_data:
                print("RGP: Invalid preset data")
                return False

            # Prepare variables for expression evaluation
            variables = {}
            if custom_properties:
                variables.update(custom_properties)

            # Add default preset properties
            preset_props = preset_data.get('properties', {})
            for prop_name, prop_info in preset_props.items():
                if prop_name in variables:
                    continue  # Custom property takes precedence
                variables[prop_name] = prop_info.get('default', 0)

            # Handle special direction multiplier
            if 'direction' in variables:
                direction = variables['direction']
                if direction == 'CCW':
                    variables['direction_multiplier'] = -1
                else:
                    variables['direction_multiplier'] = 1

            # Backup existing keyframes if requested
            scene_props = bpy.context.scene.rgp_props
            if scene_props.rgp_backup_keyframes:
                backup = self.backup_object_keyframes(obj)
                self.backup_data[obj.name] = backup

            # Clear existing keyframes if requested
            if scene_props.rgp_clear_existing:
                self.clear_object_keyframes(obj)

            # Ensure object has animation data
            if not obj.animation_data:
                obj.animation_data_create()

            if not obj.animation_data.action:
                obj.animation_data.action = bpy.data.actions.new(name=f"{obj.name}_RGP_Action")

            action = obj.animation_data.action

            # Process keyframes
            for keyframe_data in preset_data['keyframes']:
                frame_num = keyframe_data.get('frame', 1)

                # Evaluate frame number if it's an expression
                if isinstance(frame_num, str):
                    frame_num = self.evaluate_expression(frame_num, variables)

                frame_num = int(frame_num) + start_frame - 1

                # Apply location keyframes
                if 'location' in keyframe_data:
                    location = keyframe_data['location']
                    if isinstance(location, list) and len(location) >= 3:
                        for i, value in enumerate(location[:3]):
                            evaluated_value = self.evaluate_expression(value, variables)

                            # Get or create fcurve
                            fcurve = None
                            for fc in action.fcurves:
                                if fc.data_path == 'location' and fc.array_index == i:
                                    fcurve = fc
                                    break

                            if not fcurve:
                                fcurve = action.fcurves.new(data_path='location', index=i)

                            # Insert keyframe
                            kf = fcurve.keyframe_points.insert(frame_num, float(evaluated_value))

                            # Set interpolation
                            easing = keyframe_data.get('easing', 'EASE_IN_OUT')
                            self.set_keyframe_interpolation(kf, easing)

                # Apply rotation keyframes
                if 'rotation_euler' in keyframe_data:
                    rotation = keyframe_data['rotation_euler']
                    if isinstance(rotation, list) and len(rotation) >= 3:
                        for i, value in enumerate(rotation[:3]):
                            evaluated_value = self.evaluate_expression(value, variables)

                            # Get or create fcurve
                            fcurve = None
                            for fc in action.fcurves:
                                if fc.data_path == 'rotation_euler' and fc.array_index == i:
                                    fcurve = fc
                                    break

                            if not fcurve:
                                fcurve = action.fcurves.new(data_path='rotation_euler', index=i)

                            # Insert keyframe
                            kf = fcurve.keyframe_points.insert(frame_num, float(evaluated_value))

                            # Set interpolation
                            easing = keyframe_data.get('easing', 'EASE_IN_OUT')
                            self.set_keyframe_interpolation(kf, easing)

                # Apply scale keyframes
                if 'scale' in keyframe_data:
                    scale = keyframe_data['scale']
                    if isinstance(scale, list) and len(scale) >= 3:
                        for i, value in enumerate(scale[:3]):
                            evaluated_value = self.evaluate_expression(value, variables)

                            # Get or create fcurve
                            fcurve = None
                            for fc in action.fcurves:
                                if fc.data_path == 'scale' and fc.array_index == i:
                                    fcurve = fc
                                    break

                            if not fcurve:
                                fcurve = action.fcurves.new(data_path='scale', index=i)

                            # Insert keyframe
                            kf = fcurve.keyframe_points.insert(frame_num, float(evaluated_value))

                            # Set interpolation
                            easing = keyframe_data.get('easing', 'EASE_IN_OUT')
                            self.set_keyframe_interpolation(kf, easing)

                # Apply alpha keyframes (for materials)
                if 'alpha' in keyframe_data:
                    alpha_value = self.evaluate_expression(keyframe_data['alpha'], variables)
                    self.set_material_alpha_keyframe(obj, frame_num, float(alpha_value),
                                                   keyframe_data.get('easing', 'EASE_IN_OUT'))

            # Update scene to reflect changes
            bpy.context.view_layer.update()

            print(f"RGP: Applied preset '{preset_data.get('name', 'Unknown')}' to object '{obj.name}'")
            return True

        except Exception as e:
            print(f"RGP: Error applying preset to object: {e}")
            return False

    def set_keyframe_interpolation(self, keyframe, easing_type: str):
        """Set keyframe interpolation based on easing type"""
        easing_map = {
            'LINEAR': ('LINEAR', 'AUTO'),
            'EASE_IN': ('BEZIER', 'EASE_IN'),
            'EASE_OUT': ('BEZIER', 'EASE_OUT'),
            'EASE_IN_OUT': ('BEZIER', 'EASE_IN_OUT'),
            'BOUNCE': ('BOUNCE', 'AUTO'),
            'ELASTIC': ('ELASTIC', 'AUTO'),
            'BACK': ('BACK', 'AUTO'),
        }

        if easing_type in easing_map:
            interpolation, easing = easing_map[easing_type]
            keyframe.interpolation = interpolation
            if hasattr(keyframe, 'easing'):
                keyframe.easing = easing

    def set_material_alpha_keyframe(self, obj: bpy.types.Object, frame: int, alpha: float, easing: str):
        """Set alpha keyframe for object's material"""
        if not obj.material_slots or not obj.material_slots[0].material:
            return

        material = obj.material_slots[0].material

        # Ensure material uses nodes
        if not material.use_nodes:
            material.use_nodes = True

        # Find or create Principled BSDF node
        principled = None
        for node in material.node_tree.nodes:
            if node.type == 'BSDF_PRINCIPLED':
                principled = node
                break

        if not principled:
            return

        # Set alpha keyframe
        alpha_input = principled.inputs.get('Alpha')
        if alpha_input:
            alpha_input.default_value = alpha
            alpha_input.keyframe_insert(data_path="default_value", frame=frame)

            # Set interpolation for the keyframe
            if material.animation_data and material.animation_data.action:
                for fcurve in material.animation_data.action.fcurves:
                    if 'Alpha' in fcurve.data_path:
                        for kf in fcurve.keyframe_points:
                            if abs(kf.co[0] - frame) < 0.1:  # Find the keyframe we just created
                                self.set_keyframe_interpolation(kf, easing)
                                break

    def get_animation_duration(self, preset_data: Dict[str, Any], custom_properties: Dict[str, Any] = None) -> int:
        """Calculate the duration of an animation preset"""
        if not preset_data or 'keyframes' not in preset_data:
            return 30  # Default duration

        variables = {}
        if custom_properties:
            variables.update(custom_properties)

        # Add default preset properties
        preset_props = preset_data.get('properties', {})
        for prop_name, prop_info in preset_props.items():
            if prop_name not in variables:
                variables[prop_name] = prop_info.get('default', 0)

        max_frame = 1
        for keyframe_data in preset_data['keyframes']:
            frame_num = keyframe_data.get('frame', 1)

            # Evaluate frame number if it's an expression
            if isinstance(frame_num, str):
                frame_num = self.evaluate_expression(frame_num, variables)

            max_frame = max(max_frame, int(frame_num))

        return max_frame

    def validate_object_for_animation(self, obj: bpy.types.Object) -> bool:
        """Validate if object can be animated"""
        if not obj:
            return False

        # Check if object type supports animation
        supported_types = {'MESH', 'CURVE', 'SURFACE', 'META', 'FONT', 'ARMATURE', 'LATTICE', 'EMPTY', 'CAMERA', 'LIGHT'}

        return obj.type in supported_types

    def remove_animation_keyframes(self, obj: bpy.types.Object, start_frame: int, end_frame: int) -> bool:
        """Remove keyframes from object within specified frame range"""
        try:
            if not obj.animation_data or not obj.animation_data.action:
                return True  # No animation data to remove

            action = obj.animation_data.action

            # Collect keyframes to remove
            keyframes_to_remove = []

            for fcurve in action.fcurves:
                for i, keyframe in enumerate(fcurve.keyframe_points):
                    frame = keyframe.co[0]
                    if start_frame <= frame <= end_frame:
                        keyframes_to_remove.append((fcurve, i))

            # Remove keyframes in reverse order to maintain indices
            keyframes_to_remove.sort(key=lambda x: x[1], reverse=True)

            for fcurve, keyframe_index in keyframes_to_remove:
                fcurve.keyframe_points.remove(fcurve.keyframe_points[keyframe_index])

            # Remove empty fcurves
            fcurves_to_remove = []
            for fcurve in action.fcurves:
                if len(fcurve.keyframe_points) == 0:
                    fcurves_to_remove.append(fcurve)

            for fcurve in fcurves_to_remove:
                action.fcurves.remove(fcurve)

            # Remove action if no fcurves left
            if len(action.fcurves) == 0:
                bpy.data.actions.remove(action)
                obj.animation_data.action = None

            print(f"RGP: Removed keyframes from frames {start_frame}-{end_frame} for object '{obj.name}'")
            return True

        except Exception as e:
            print(f"RGP: Error removing keyframes: {e}")
            return False

    def backup_object_transform(self, obj: bpy.types.Object) -> Dict[str, Any]:
        """Backup object's current transform"""
        return {
            'location': obj.location.copy(),
            'rotation_euler': obj.rotation_euler.copy(),
            'scale': obj.scale.copy()
        }

    def restore_object_transform(self, obj: bpy.types.Object, transform_backup: Dict[str, Any]):
        """Restore object's transform from backup"""
        try:
            if 'location' in transform_backup:
                obj.location = transform_backup['location']
            if 'rotation_euler' in transform_backup:
                obj.rotation_euler = transform_backup['rotation_euler']
            if 'scale' in transform_backup:
                obj.scale = transform_backup['scale']

            # Update the scene
            bpy.context.view_layer.update()

            print(f"RGP: Restored transform for object '{obj.name}'")

        except Exception as e:
            print(f"RGP: Error restoring transform: {e}")

    def get_animation_frame_range(self, animation_item) -> Tuple[int, int]:
        """Get the actual frame range used by an animation"""
        start_frame = animation_item.rgp_start_frame
        end_frame = animation_item.rgp_end_frame

        # If we have keyframe data, use that for more precise range
        if len(animation_item.rgp_keyframes) > 0:
            keyframe_frames = [kf.rgp_frame + start_frame - 1 for kf in animation_item.rgp_keyframes]
            actual_start = min(keyframe_frames)
            actual_end = max(keyframe_frames)

            # Expand range slightly to catch any interpolated frames
            return (max(1, actual_start - 1), actual_end + 1)

        return (start_frame, end_frame)

    def update_animation_timing(self, animation_index, new_start_frame, new_end_frame, new_duration):
        """Update animation timing by moving existing keyframes instead of recreating them"""
        try:
            scene_props = bpy.context.scene.rgp_props

            if animation_index >= len(scene_props.rgp_animations):
                return

            anim = scene_props.rgp_animations[animation_index]

            if not anim.rgp_target_object:
                return

            obj = anim.rgp_target_object

            # Store old timing
            old_start = anim.rgp_start_frame
            old_end = anim.rgp_end_frame

            # Calculate new timing
            new_duration_calc = new_end_frame - new_start_frame + 1

            # Update duration property if it was changed directly
            if new_duration != new_duration_calc:
                new_end_frame = new_start_frame + new_duration - 1
                anim.rgp_end_frame = new_end_frame

            # If timing hasn't actually changed, return
            if old_start == new_start_frame and old_end == new_end_frame:
                return

            # Get animation data
            if not obj.animation_data or not obj.animation_data.action:
                return

            action = obj.animation_data.action
            old_duration = old_end - old_start
            new_duration_actual = new_end_frame - new_start_frame

            # Calculate scaling factor
            scale_factor = new_duration_actual / old_duration if old_duration > 0 else 1.0

            # Move existing keyframes instead of recreating them
            for fcurve in action.fcurves:
                for keyframe in fcurve.keyframe_points:
                    old_frame = keyframe.co[0]

                    # Check if keyframe is in the old range
                    if old_start <= old_frame <= old_end:
                        # Calculate relative position (0.0 to 1.0)
                        relative_pos = (old_frame - old_start) / old_duration if old_duration > 0 else 0.0

                        # Calculate new frame position
                        new_frame = new_start_frame + (relative_pos * new_duration_actual)

                        # Move the keyframe by updating its coordinates
                        keyframe.co[0] = new_frame
                        keyframe.handle_left[0] = new_frame
                        keyframe.handle_right[0] = new_frame

            # Sort keyframes after modification
            for fcurve in action.fcurves:
                fcurve.keyframe_points.sort()

            # Update scene
            bpy.context.view_layer.update()

            print(f"RGP: Moved keyframes from {old_start}-{old_end} to {new_start_frame}-{new_end_frame}")

        except Exception as e:
            print(f"RGP: Error updating animation timing: {e}")

    def update_animation_timing_direct(self, target_obj, new_start_frame, new_end_frame):
        """Update animation timing directly for a specific object"""
        try:
            if not target_obj or not target_obj.animation_data or not target_obj.animation_data.action:
                return

            action = target_obj.animation_data.action

            # Get current keyframe range
            all_frames = []
            for fcurve in action.fcurves:
                for keyframe in fcurve.keyframe_points:
                    all_frames.append(keyframe.co[0])

            if not all_frames:
                return

            old_start = min(all_frames)
            old_end = max(all_frames)
            old_duration = old_end - old_start

            # If no change needed, return
            if abs(old_start - new_start_frame) < 0.1 and abs(old_end - new_end_frame) < 0.1:
                return

            new_duration = new_end_frame - new_start_frame

            # Calculate scaling factor
            scale_factor = new_duration / old_duration if old_duration > 0 else 1.0

            # Update all keyframes
            for fcurve in action.fcurves:
                for keyframe in fcurve.keyframe_points:
                    old_frame = keyframe.co[0]

                    # Calculate relative position (0.0 to 1.0)
                    if old_duration > 0:
                        relative_pos = (old_frame - old_start) / old_duration
                    else:
                        relative_pos = 0.0

                    # Calculate new frame position
                    new_frame = new_start_frame + (relative_pos * new_duration)

                    # Update keyframe
                    keyframe.co[0] = new_frame
                    keyframe.handle_left[0] = new_frame
                    keyframe.handle_right[0] = new_frame

            # Sort keyframes after modification
            for fcurve in action.fcurves:
                fcurve.keyframe_points.sort()

            # Update scene
            bpy.context.view_layer.update()

            print(f"RGP: Updated timing for {target_obj.name}: {old_start:.1f}-{old_end:.1f} -> {new_start_frame}-{new_end_frame}")

        except Exception as e:
            print(f"RGP: Error updating direct timing: {e}")

# Global animation engine instance
rgp_animation_engine = RGP_AnimationEngine()

def get_animation_engine() -> RGP_AnimationEngine:
    """Get the global animation engine instance"""
    return rgp_animation_engine

def register():
    """Register animation core"""
    print("RGP: Animation core registered")

def unregister():
    """Unregister animation core"""
    print("RGP: Animation core unregistered")