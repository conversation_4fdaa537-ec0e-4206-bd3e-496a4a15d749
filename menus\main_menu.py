"""
Rigaddon Play - Main Menu
Context menus and quick access menus for the addon
"""

import bpy
from bpy.types import Menu
from ..presets.utils_presets import get_preset_manager

class RGP_MT_PresetMenu(Menu):
    """Quick preset selection menu"""
    bl_label = "Rigaddon Play Presets"
    bl_idname = "RGP_MT_preset_menu"

    def draw(self, context):
        layout = self.layout
        preset_manager = get_preset_manager()

        # Get presets by category
        categories = preset_manager.get_categories()
        presets = preset_manager.get_animation_presets()

        if not presets:
            layout.label(text="No presets available", icon='INFO')
            return

        # Group presets by category
        for category in categories:
            category_name = category.get('name', 'Unknown')
            category_presets = preset_manager.get_presets_by_category(category_name)

            if category_presets:
                layout.separator()
                layout.label(text=category_name, icon=category.get('icon', 'PRESET'))

                for preset_name, preset_data in category_presets.items():
                    op = layout.operator("rgp.apply_preset", text=preset_data.get('name', preset_name))
                    op.preset_name = preset_name

class RGP_MT_AnimationMenu(Menu):
    """Animation control menu"""
    bl_label = "Rigaddon Play Controls"
    bl_idname = "RGP_MT_animation_menu"

    def draw(self, context):
        layout = self.layout
        scene_props = context.scene.rgp_props

        # Playback controls
        layout.label(text="Playback Controls", icon='PLAY')
        layout.operator("rgp.play_all_animations", text="Play All", icon='PLAY')
        layout.operator("rgp.stop_animation", text="Stop", icon='PAUSE')

        layout.separator()

        # Animation list controls
        if len(scene_props.rgp_animations) > 0:
            layout.label(text="Animation List", icon='SEQUENCE')

            # Show current animation
            if scene_props.rgp_active_animation_index < len(scene_props.rgp_animations):
                active_anim = scene_props.rgp_animations[scene_props.rgp_active_animation_index]
                layout.label(text=f"Active: {active_anim.rgp_name}", icon='RADIOBUT_ON')

                # Quick controls for active animation
                play_op = layout.operator("rgp.play_animation", text="Play Active", icon='PLAY')
                play_op.animation_index = scene_props.rgp_active_animation_index

                preview_op = layout.operator("rgp.preview_animation", text="Preview Active", icon='HIDE_OFF')
                preview_op.animation_index = scene_props.rgp_active_animation_index

            layout.separator()
            layout.operator("rgp.clear_all_animations", text="Clear All", icon='TRASH')
        else:
            layout.label(text="No animations", icon='INFO')

class RGP_MT_UtilsMenu(Menu):
    """Utilities menu"""
    bl_label = "Rigaddon Play Utils"
    bl_idname = "RGP_MT_utils_menu"

    def draw(self, context):
        layout = self.layout

        # Import/Export
        layout.label(text="Import/Export", icon='IMPORT')
        layout.operator("rgp.export_animations", text="Export Animations", icon='EXPORT')
        layout.operator("rgp.import_animations", text="Import Animations", icon='IMPORT')

        layout.separator()

        # Preset management
        layout.label(text="Preset Management", icon='PRESET')
        layout.operator("rgp.reload_presets", text="Reload Presets", icon='FILE_REFRESH')
        layout.operator("rgp.create_custom_preset", text="Create Custom Preset", icon='ADD')

        layout.separator()

        # Advanced tools
        layout.label(text="Advanced Tools", icon='TOOL_SETTINGS')
        layout.operator("rgp.bake_animation", text="Bake Animation", icon='RENDER_ANIMATION')

        layout.separator()

        # Help
        layout.label(text="Help", icon='HELP')
        layout.operator("rgp.open_documentation", text="Documentation", icon='HELP')

def draw_object_context_menu(self, context):
    """Add Rigaddon Play options to object context menu"""
    if context.active_object:
        layout = self.layout
        layout.separator()
        layout.menu("RGP_MT_preset_menu", text="Rigaddon Play Presets", icon='PLAY')

def draw_animation_context_menu(self, context):
    """Add Rigaddon Play options to animation context menu"""
    layout = self.layout
    layout.separator()
    layout.menu("RGP_MT_animation_menu", text="Rigaddon Play Controls", icon='PLAY')

# Registration
classes = [
    RGP_MT_PresetMenu,
    RGP_MT_AnimationMenu,
    RGP_MT_UtilsMenu,
]

def register():
    """Register menus"""
    from bpy.utils import register_class

    for cls in classes:
        register_class(cls)

    # Add to context menus
    bpy.types.VIEW3D_MT_object_context_menu.append(draw_object_context_menu)
    bpy.types.DOPESHEET_MT_context_menu.append(draw_animation_context_menu)

    print("RGP: Menus registered")

def unregister():
    """Unregister menus"""
    from bpy.utils import unregister_class

    # Remove from context menus
    bpy.types.DOPESHEET_MT_context_menu.remove(draw_animation_context_menu)
    bpy.types.VIEW3D_MT_object_context_menu.remove(draw_object_context_menu)

    for cls in reversed(classes):
        unregister_class(cls)

    print("RGP: Menus unregistered")