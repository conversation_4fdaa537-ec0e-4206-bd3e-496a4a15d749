"""
Rigaddon Play - Add Animation Operators
Operators for adding and applying animation presets
"""

import bpy
from bpy.types import Operator
from bpy.props import StringProperty, IntProperty, BoolProperty
from ..presets.utils_presets import get_preset_manager
from ..utils.animation_core import get_animation_engine

class RGP_OT_ApplyPreset(Operator):
    """Apply animation preset to selected object"""
    bl_idname = "rgp.apply_preset"
    bl_label = "Apply Animation Preset"
    bl_description = "Apply selected animation preset to the active object"
    bl_options = {'REGISTER', 'UNDO'}

    preset_name: StringProperty(
        name="Preset Name",
        description="Name of the preset to apply",
        default=""
    )

    start_frame: IntProperty(
        name="Start Frame",
        description="Starting frame for the animation",
        default=1,
        min=1
    )

    add_to_list: BoolProperty(
        name="Add to Animation List",
        description="Add this animation to the animation list",
        default=True
    )

    @classmethod
    def poll(cls, context):
        return context.active_object is not None

    def execute(self, context):
        try:
            active_obj = context.active_object
            scene_props = context.scene.rgp_props
            preset_manager = get_preset_manager()
            animation_engine = get_animation_engine()

            # Get preset data
            preset_data = preset_manager.get_preset(self.preset_name)
            if not preset_data:
                self.report({'ERROR'}, f"Preset '{self.preset_name}' not found")
                return {'CANCELLED'}

            # Validate object for animation
            if not animation_engine.validate_object_for_animation(active_obj):
                self.report({'ERROR'}, f"Object '{active_obj.name}' cannot be animated")
                return {'CANCELLED'}

            # Calculate animation duration
            duration = animation_engine.get_animation_duration(preset_data)
            end_frame = self.start_frame + duration - 1

            # Apply preset to object
            success = animation_engine.apply_preset_to_object(
                active_obj,
                preset_data,
                start_frame=self.start_frame
            )

            if not success:
                self.report({'ERROR'}, "Failed to apply animation preset")
                return {'CANCELLED'}

            # Add to animation list if requested
            if self.add_to_list:
                anim_item = scene_props.rgp_animations.add()
                anim_item.rgp_name = f"{preset_data.get('name', self.preset_name)} - {active_obj.name}"
                anim_item.rgp_preset_name = self.preset_name
                anim_item.rgp_target_object = active_obj
                anim_item.rgp_start_frame = self.start_frame
                anim_item.rgp_end_frame = end_frame
                anim_item.rgp_duration = duration

                # Backup original transform
                anim_item.rgp_backup_location = active_obj.location.copy()
                anim_item.rgp_backup_rotation = active_obj.rotation_euler.copy()
                anim_item.rgp_backup_scale = active_obj.scale.copy()
                anim_item.rgp_has_backup = True

                # Set as active animation
                scene_props.rgp_active_animation_index = len(scene_props.rgp_animations) - 1

            self.report({'INFO'}, f"Applied '{preset_data.get('name', self.preset_name)}' to '{active_obj.name}'")
            return {'FINISHED'}

        except Exception as e:
            self.report({'ERROR'}, f"Error applying preset: {str(e)}")
            return {'CANCELLED'}

class RGP_OT_ShowPresetInfo(Operator):
    """Show information about a preset"""
    bl_idname = "rgp.show_preset_info"
    bl_label = "Show Preset Info"
    bl_description = "Show detailed information about this preset"

    preset_name: StringProperty(
        name="Preset Name",
        description="Name of the preset to show info for",
        default=""
    )

    def execute(self, context):
        preset_manager = get_preset_manager()
        preset_data = preset_manager.get_preset(self.preset_name)

        if not preset_data:
            self.report({'ERROR'}, f"Preset '{self.preset_name}' not found")
            return {'CANCELLED'}

        # Show info in a popup
        def draw_popup(self, context):
            layout = self.layout

            layout.label(text=f"Preset: {preset_data.get('name', self.preset_name)}", icon='INFO')
            layout.separator()

            layout.label(text=f"Category: {preset_data.get('category', 'Unknown')}")
            layout.label(text=f"Description: {preset_data.get('description', 'No description')}")

            if 'properties' in preset_data:
                layout.separator()
                layout.label(text="Properties:")
                for prop_name, prop_info in preset_data['properties'].items():
                    layout.label(text=f"  {prop_name}: {prop_info.get('description', 'No description')}")

            if 'keyframes' in preset_data:
                layout.separator()
                layout.label(text=f"Keyframes: {len(preset_data['keyframes'])}")

        context.window_manager.popup_menu(draw_popup, title="Preset Information", icon='INFO')
        return {'FINISHED'}

class RGP_OT_CreateCustomPreset(Operator):
    """Create a custom animation preset"""
    bl_idname = "rgp.create_custom_preset"
    bl_label = "Create Custom Preset"
    bl_description = "Create a new custom animation preset"
    bl_options = {'REGISTER', 'UNDO'}

    preset_name: StringProperty(
        name="Preset Name",
        description="Name for the new preset",
        default="Custom Animation"
    )

    preset_description: StringProperty(
        name="Description",
        description="Description of the preset",
        default=""
    )

    def invoke(self, context, event):
        return context.window_manager.invoke_props_dialog(self)

    def execute(self, context):
        try:
            active_obj = context.active_object

            if not active_obj:
                self.report({'ERROR'}, "No active object selected")
                return {'CANCELLED'}

            # Create basic preset structure
            preset_data = {
                "name": self.preset_name,
                "description": self.preset_description,
                "category": "Custom",
                "icon": "SETTINGS",
                "properties": {},
                "keyframes": [
                    {"frame": 1, "location": [0, 0, 0], "easing": "EASE_IN_OUT"},
                    {"frame": 30, "location": [0, 0, 0], "easing": "EASE_IN_OUT"}
                ]
            }

            # Save preset
            preset_manager = get_preset_manager()
            success = preset_manager.add_custom_preset(self.preset_name, preset_data)

            if success:
                self.report({'INFO'}, f"Created custom preset '{self.preset_name}'")
                return {'FINISHED'}
            else:
                self.report({'ERROR'}, "Failed to create custom preset")
                return {'CANCELLED'}

        except Exception as e:
            self.report({'ERROR'}, f"Error creating preset: {str(e)}")
            return {'CANCELLED'}

class RGP_OT_ReloadPresets(Operator):
    """Reload animation presets from file"""
    bl_idname = "rgp.reload_presets"
    bl_label = "Reload Presets"
    bl_description = "Reload animation presets from JSON file"

    def execute(self, context):
        try:
            preset_manager = get_preset_manager()
            success = preset_manager.load_presets()

            if success:
                self.report({'INFO'}, "Presets reloaded successfully")
                return {'FINISHED'}
            else:
                self.report({'WARNING'}, "Failed to reload presets")
                return {'CANCELLED'}

        except Exception as e:
            self.report({'ERROR'}, f"Error reloading presets: {str(e)}")
            return {'CANCELLED'}

class RGP_OT_AddAnimationPopup(Operator):
    """Add animation with preset selection popup"""
    bl_idname = "rgp.add_animation_popup"
    bl_label = "Add Animation"
    bl_description = "Add new animation with preset selection"
    bl_options = {'REGISTER', 'UNDO'}

    @classmethod
    def poll(cls, context):
        return context.active_object is not None

    def invoke(self, context, event):
        return context.window_manager.invoke_props_dialog(self, width=500)

    def draw(self, context):
        layout = self.layout

        # Get preset items for display
        try:
            preset_manager = get_preset_manager()
            presets = preset_manager.get_animation_presets()

            if presets:
                # Create a column for preset selection
                col = layout.column()
                col.label(text="Select Animation Preset:", icon='SEQUENCE')
                col.separator()

                # Create preset buttons organized by category
                categories = {}
                for name, preset_data in sorted(presets.items()):
                    category = preset_data.get('category', 'Unknown')
                    if category not in categories:
                        categories[category] = []
                    categories[category].append((name, preset_data))

                # Display presets by category
                for category in sorted(categories.keys()):
                    if len(categories) > 1:  # Only show category headers if multiple categories
                        col.separator()
                        header = col.row()
                        header.label(text=f"{category}:", icon='BOOKMARKS')

                    # Create a grid for presets in this category
                    grid = col.grid_flow(columns=2, align=True)
                    for name, preset_data in categories[category]:
                        op = grid.operator("rgp.add_animation_popup_execute", text=name)
                        op.preset_name = name
            else:
                layout.label(text="No presets available", icon='ERROR')
        except Exception as e:
            layout.label(text=f"Error loading presets: {str(e)}", icon='ERROR')

    def execute(self, context):
        return {'FINISHED'}

class RGP_OT_AddAnimationPopupExecute(Operator):
    """Execute adding animation with selected preset"""
    bl_idname = "rgp.add_animation_popup_execute"
    bl_label = "Add Animation"
    bl_description = "Add animation with selected preset"
    bl_options = {'REGISTER', 'UNDO'}

    preset_name: StringProperty(
        name="Preset Name",
        description="Name of the preset to apply",
        default=""
    )

    def execute(self, context):
        if not self.preset_name:
            self.report({'ERROR'}, "No preset selected")
            return {'CANCELLED'}

        # Use the existing apply preset operator
        bpy.ops.rgp.apply_preset(preset_name=self.preset_name, add_to_list=True)
        return {'FINISHED'}

# Registration
classes = [
    RGP_OT_ApplyPreset,
    RGP_OT_ShowPresetInfo,
    RGP_OT_CreateCustomPreset,
    RGP_OT_ReloadPresets,
    RGP_OT_AddAnimationPopup,
    RGP_OT_AddAnimationPopupExecute,
]

def register():
    """Register add animation operators"""
    from bpy.utils import register_class

    for cls in classes:
        register_class(cls)

    print("RGP: Add animation operators registered")

def unregister():
    """Unregister add animation operators"""
    from bpy.utils import unregister_class

    for cls in reversed(classes):
        unregister_class(cls)

    print("RGP: Add animation operators unregistered")