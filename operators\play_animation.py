"""
Rigaddon Play - Play Animation Operators
Operators for playing, stopping, and controlling animation playback
"""

import bpy
from bpy.types import Operator
from bpy.props import StringProperty, IntProperty, EnumProperty
from ..utils.playback import get_playback_manager

class RGP_OT_PlayAnimation(Operator):
    """Play a specific animation"""
    bl_idname = "rgp.play_animation"
    bl_label = "Play Animation"
    bl_description = "Play the selected animation"
    bl_options = {'REGISTER'}

    animation_index: IntProperty(
        name="Animation Index",
        description="Index of animation to play",
        default=-1
    )

    mode: EnumProperty(
        name="Playback Mode",
        description="How to play the animation",
        items=[
            ('SINGLE', 'Single', 'Play only this animation'),
            ('FROM_SELECTED', 'From Selected', 'Play from this animation to the end'),
            ('LOOP_SELECTED', 'Loop Selected', 'Loop only this animation'),
        ],
        default='SINGLE'
    )

    def execute(self, context):
        try:
            scene_props = context.scene.rgp_props

            # Get animation to play
            if self.animation_index >= 0:
                if self.animation_index >= len(scene_props.rgp_animations):
                    self.report({'ERROR'}, "Invalid animation index")
                    return {'CANCELLED'}
                animation_item = scene_props.rgp_animations[self.animation_index]
            else:
                # Use currently selected animation
                if len(scene_props.rgp_animations) == 0:
                    self.report({'ERROR'}, "No animations available")
                    return {'CANCELLED'}

                if scene_props.rgp_active_animation_index >= len(scene_props.rgp_animations):
                    self.report({'ERROR'}, "No animation selected")
                    return {'CANCELLED'}

                animation_item = scene_props.rgp_animations[scene_props.rgp_active_animation_index]

            # Check if animation is active
            if not animation_item.rgp_is_active:
                self.report({'WARNING'}, f"Animation '{animation_item.rgp_name}' is disabled")
                return {'CANCELLED'}

            # Get target object first
            target_obj = animation_item.rgp_target_object
            if not target_obj:
                self.report({'ERROR'}, f"No target object set for animation '{animation_item.rgp_name}'")
                return {'CANCELLED'}

            # Check if object still exists in scene
            if target_obj.name not in bpy.data.objects:
                self.report({'ERROR'}, f"Target object '{target_obj.name}' no longer exists")
                return {'CANCELLED'}

            # Stop ALL other playing animations first (smart playback)
            bpy.ops.screen.animation_cancel()  # Stop any current playback

            for anim in scene_props.rgp_animations:
                if anim != animation_item and anim.rgp_is_playing:
                    anim.rgp_is_playing = False
                    # Clear keyframes for other animations to prevent conflicts
                    if anim.rgp_target_object and anim.rgp_target_object.name in bpy.data.objects:
                        self.clear_animation_keyframes(anim.rgp_target_object, anim.rgp_start_frame, anim.rgp_end_frame)

            # Mark this animation as playing
            animation_item.rgp_is_playing = True

            # Get animation engine and apply preset
            from ..utils.animation_core import get_animation_engine
            from ..presets.utils_presets import get_preset_manager

            animation_engine = get_animation_engine()
            preset_manager = get_preset_manager()

            if not animation_engine:
                self.report({'ERROR'}, "Animation engine not available")
                return {'CANCELLED'}

            # Get preset data
            preset_data = preset_manager.get_preset(animation_item.rgp_preset_name)
            if not preset_data:
                self.report({'ERROR'}, f"Preset '{animation_item.rgp_preset_name}' not found")
                return {'CANCELLED'}

            # Apply the preset to create keyframes
            success = animation_engine.apply_preset_to_object(
                target_obj,
                preset_data,
                start_frame=animation_item.rgp_start_frame
            )

            if not success:
                animation_item.rgp_is_playing = False
                self.report({'ERROR'}, f"Failed to apply preset '{animation_item.rgp_preset_name}'")
                return {'CANCELLED'}

            # Set timeline to animation start frame
            context.scene.frame_set(animation_item.rgp_start_frame)

            # Start playback
            bpy.ops.screen.animation_play()

            self.report({'INFO'}, f"Playing animation '{animation_item.rgp_name}'")
            return {'FINISHED'}

        except Exception as e:
            self.report({'ERROR'}, f"Error playing animation: {str(e)}")
            return {'CANCELLED'}

    def clear_animation_keyframes(self, obj, start_frame, end_frame):
        """Clear keyframes for an object in specified range"""
        try:
            if not obj.animation_data or not obj.animation_data.action:
                return

            action = obj.animation_data.action

            # Clear keyframes in specified range
            for fcurve in action.fcurves:
                keyframes_to_remove = []
                for keyframe in fcurve.keyframe_points:
                    frame = keyframe.co[0]
                    if start_frame <= frame <= end_frame:
                        keyframes_to_remove.append(keyframe)

                # Remove keyframes
                for keyframe in keyframes_to_remove:
                    fcurve.keyframe_points.remove(keyframe)

        except Exception as e:
            print(f"RGP: Error clearing keyframes: {e}")

class RGP_OT_PlayWithMode(Operator):
    """Play animation based on selected mode"""
    bl_idname = "rgp.play_with_mode"
    bl_label = "Play Animation"
    bl_description = "Play animation according to the selected playback mode"
    bl_options = {'REGISTER'}

    def execute(self, context):
        try:
            scene_props = context.scene.rgp_props
            playback_mode = scene_props.rgp_playback_mode

            if len(scene_props.rgp_animations) == 0:
                self.report({'ERROR'}, "No animations available")
                return {'CANCELLED'}

            if playback_mode == 'SINGLE':
                # Play only selected animation
                if scene_props.rgp_active_animation_index >= len(scene_props.rgp_animations):
                    self.report({'ERROR'}, "No animation selected")
                    return {'CANCELLED'}

                bpy.ops.rgp.play_animation(animation_index=scene_props.rgp_active_animation_index)

            elif playback_mode == 'ALL':
                # Play all active animations using the dedicated operator
                bpy.ops.rgp.play_all_animations()

            elif playback_mode == 'FROM_SELECTED':
                # Play from selected animation to the end
                start_index = scene_props.rgp_active_animation_index
                if start_index >= len(scene_props.rgp_animations):
                    self.report({'ERROR'}, "No animation selected")
                    return {'CANCELLED'}

                played_count = 0
                for i in range(start_index, len(scene_props.rgp_animations)):
                    anim = scene_props.rgp_animations[i]
                    if anim.rgp_is_active:
                        bpy.ops.rgp.play_animation(animation_index=i)
                        played_count += 1

                if played_count == 0:
                    self.report({'WARNING'}, "No active animations from selected position")
                else:
                    self.report({'INFO'}, f"Playing {played_count} animations from selected")

            elif playback_mode == 'LOOP_SELECTED':
                # Loop only selected animation
                if scene_props.rgp_active_animation_index >= len(scene_props.rgp_animations):
                    self.report({'ERROR'}, "No animation selected")
                    return {'CANCELLED'}

                active_anim = scene_props.rgp_animations[scene_props.rgp_active_animation_index]
                if not active_anim.rgp_is_active:
                    self.report({'WARNING'}, "Selected animation is not active")
                    return {'CANCELLED'}

                # Set animation to loop and play
                active_anim.rgp_is_looping = True
                active_anim.rgp_loop_count = -1  # Infinite loop
                bpy.ops.rgp.play_animation(animation_index=scene_props.rgp_active_animation_index)
                self.report({'INFO'}, f"Looping '{active_anim.rgp_name}'")

            return {'FINISHED'}

        except Exception as e:
            self.report({'ERROR'}, f"Error playing animation: {str(e)}")
            return {'CANCELLED'}

class RGP_OT_PlayAllAnimations(Operator):
    """Play all active animations"""
    bl_idname = "rgp.play_all_animations"
    bl_label = "Play All Animations"
    bl_description = "Play all active animations in sequence"
    bl_options = {'REGISTER'}

    def execute(self, context):
        try:
            scene_props = context.scene.rgp_props
            playback_manager = get_playback_manager()

            # Get all active animations
            active_animations = [anim for anim in scene_props.rgp_animations if anim.rgp_is_active]

            if not active_animations:
                self.report({'WARNING'}, "No active animations found")
                return {'CANCELLED'}

            # Play all animations
            success = playback_manager.play_all_animations(active_animations)

            if success:
                self.report({'INFO'}, f"Playing {len(active_animations)} animations")
                return {'FINISHED'}
            else:
                self.report({'ERROR'}, "Failed to play animations")
                return {'CANCELLED'}

        except Exception as e:
            self.report({'ERROR'}, f"Error playing animations: {str(e)}")
            return {'CANCELLED'}

class RGP_OT_PreviewAnimation(Operator):
    """Preview selected animation"""
    bl_idname = "rgp.preview_animation"
    bl_label = "Preview Animation"
    bl_description = "Preview the selected animation without affecting timeline"
    bl_options = {'REGISTER'}

    animation_index: IntProperty(
        name="Animation Index",
        description="Index of animation to preview",
        default=-1
    )

    def execute(self, context):
        try:
            scene_props = context.scene.rgp_props

            # Determine which animation to preview
            if self.animation_index >= 0:
                index = self.animation_index
            else:
                index = scene_props.rgp_active_animation_index

            # Validate index
            if index < 0 or index >= len(scene_props.rgp_animations):
                self.report({'ERROR'}, "No animation selected")
                return {'CANCELLED'}

            animation_item = scene_props.rgp_animations[index]

            # Get target object
            target_obj = animation_item.rgp_target_object
            if not target_obj:
                self.report({'ERROR'}, f"No target object set for animation '{animation_item.rgp_name}'")
                return {'CANCELLED'}

            # Check if object still exists in scene
            if target_obj.name not in bpy.data.objects:
                self.report({'ERROR'}, f"Target object '{target_obj.name}' no longer exists")
                return {'CANCELLED'}

            # Store current frame
            original_frame = context.scene.frame_current

            # Set timeline to animation start
            context.scene.frame_set(animation_item.rgp_start_frame)

            # Play animation briefly
            bpy.ops.screen.animation_play()

            # Schedule stop after duration
            def stop_preview():
                bpy.ops.screen.animation_cancel()
                context.scene.frame_set(original_frame)
                return None

            # Use timer to stop preview
            bpy.app.timers.register(stop_preview, first_interval=2.0)

            self.report({'INFO'}, f"Previewing '{animation_item.rgp_name}'")
            return {'FINISHED'}

        except Exception as e:
            self.report({'ERROR'}, f"Error previewing animation: {str(e)}")
            return {'CANCELLED'}

class RGP_OT_StopAnimation(Operator):
    """Stop animation playback"""
    bl_idname = "rgp.stop_animation"
    bl_label = "Stop Animation"
    bl_description = "Stop current animation playback"
    bl_options = {'REGISTER'}

    def execute(self, context):
        try:
            scene_props = context.scene.rgp_props

            # Stop Blender's animation playback
            bpy.ops.screen.animation_cancel()

            # Mark all animations as not playing
            for anim in scene_props.rgp_animations:
                anim.rgp_is_playing = False

            # Try to use playback manager if available
            try:
                playback_manager = get_playback_manager()
                playback_manager.stop_animation()
            except:
                pass  # Fallback if playback manager not available

            self.report({'INFO'}, "Animation playback stopped")
            return {'FINISHED'}

        except Exception as e:
            self.report({'ERROR'}, f"Error stopping animation: {str(e)}")
            return {'CANCELLED'}

class RGP_OT_PauseAnimation(Operator):
    """Pause animation playback"""
    bl_idname = "rgp.pause_animation"
    bl_label = "Pause Animation"
    bl_description = "Pause current animation playback"
    bl_options = {'REGISTER'}

    def execute(self, context):
        try:
            playback_manager = get_playback_manager()
            playback_manager.pause_animation()

            self.report({'INFO'}, "Animation playback paused")
            return {'FINISHED'}

        except Exception as e:
            self.report({'ERROR'}, f"Error pausing animation: {str(e)}")
            return {'CANCELLED'}

class RGP_OT_ResumeAnimation(Operator):
    """Resume animation playback"""
    bl_idname = "rgp.resume_animation"
    bl_label = "Resume Animation"
    bl_description = "Resume paused animation playback"
    bl_options = {'REGISTER'}

    def execute(self, context):
        try:
            playback_manager = get_playback_manager()
            playback_manager.resume_animation()

            self.report({'INFO'}, "Animation playback resumed")
            return {'FINISHED'}

        except Exception as e:
            self.report({'ERROR'}, f"Error resuming animation: {str(e)}")
            return {'CANCELLED'}

class RGP_OT_PreviewAnimation(Operator):
    """Preview animation without full playback"""
    bl_idname = "rgp.preview_animation"
    bl_label = "Preview Animation"
    bl_description = "Preview the selected animation"
    bl_options = {'REGISTER'}

    animation_index: IntProperty(
        name="Animation Index",
        description="Index of animation to preview",
        default=-1
    )

    def execute(self, context):
        try:
            scene_props = context.scene.rgp_props
            scene = context.scene

            # Get animation to preview
            if self.animation_index >= 0:
                if self.animation_index >= len(scene_props.rgp_animations):
                    self.report({'ERROR'}, "Invalid animation index")
                    return {'CANCELLED'}
                animation_item = scene_props.rgp_animations[self.animation_index]
            else:
                # Use currently selected animation
                if len(scene_props.rgp_animations) == 0:
                    self.report({'ERROR'}, "No animations available")
                    return {'CANCELLED'}

                if scene_props.rgp_active_animation_index >= len(scene_props.rgp_animations):
                    self.report({'ERROR'}, "No animation selected")
                    return {'CANCELLED'}

                animation_item = scene_props.rgp_animations[scene_props.rgp_active_animation_index]

            # Set timeline to animation range
            scene.frame_start = animation_item.rgp_start_frame
            scene.frame_end = animation_item.rgp_end_frame
            scene.frame_set(animation_item.rgp_start_frame)

            self.report({'INFO'}, f"Previewing animation '{animation_item.rgp_name}'")
            return {'FINISHED'}

        except Exception as e:
            self.report({'ERROR'}, f"Error previewing animation: {str(e)}")
            return {'CANCELLED'}

# Registration
classes = [
    RGP_OT_PlayAnimation,
    RGP_OT_PlayWithMode,
    RGP_OT_PlayAllAnimations,
    RGP_OT_StopAnimation,
    RGP_OT_PauseAnimation,
    RGP_OT_ResumeAnimation,
    RGP_OT_PreviewAnimation,
]

def register():
    """Register play animation operators"""
    from bpy.utils import register_class

    for cls in classes:
        register_class(cls)

    print("RGP: Play animation operators registered")

def unregister():
    """Unregister play animation operators"""
    from bpy.utils import unregister_class

    for cls in reversed(classes):
        unregister_class(cls)

    print("RGP: Play animation operators unregistered")