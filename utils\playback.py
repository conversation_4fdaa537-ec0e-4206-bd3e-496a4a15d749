"""
Rigaddon Play - Playback Utilities
Animation playback control and management
"""

import bpy
from typing import List, Optional
import time

class RGP_PlaybackManager:
    """Manager for animation playback control"""

    def __init__(self):
        self.is_playing = False
        self.current_animation = None
        self.loop_count = 0
        self.max_loops = 1
        self.playback_timer = None

    def play_animation(self, animation_item, mode: str = 'SINGLE') -> bool:
        """Play a specific animation"""
        try:
            scene = bpy.context.scene
            scene_props = scene.rgp_props

            if not animation_item:
                print("RGP: No animation item provided")
                return False

            # Get the target object
            target_obj = animation_item.rgp_target_object
            if not target_obj:
                print(f"RGP: No target object set for animation '{animation_item.rgp_name}'")
                return False

            # Check if object still exists in scene
            if target_obj.name not in bpy.data.objects:
                print(f"RGP: Target object '{target_obj.name}' no longer exists")
                return False

            # Set timeline range
            start_frame = animation_item.rgp_start_frame
            end_frame = animation_item.rgp_end_frame

            if mode == 'SINGLE':
                scene.frame_start = start_frame
                scene.frame_end = end_frame
            elif mode == 'FROM_SELECTED':
                scene.frame_start = start_frame
                # Keep existing end frame or extend if needed
                scene.frame_end = max(scene.frame_end, end_frame)

            # Set current frame to start
            scene.frame_set(start_frame)

            # Apply global speed multiplier
            if hasattr(scene_props, 'rgp_global_speed'):
                # This would require modifying the action's fcurves
                self.apply_speed_multiplier(target_obj, scene_props.rgp_global_speed)

            # Start playback
            if not bpy.context.screen.is_animation_playing:
                bpy.ops.screen.animation_play()

            self.is_playing = True
            self.current_animation = animation_item

            # Handle looping
            if animation_item.rgp_is_looping:
                self.max_loops = animation_item.rgp_loop_count
                self.loop_count = 0

                if self.max_loops != -1:  # Not infinite loop
                    # Set up timer to stop after specified loops
                    self.setup_loop_timer(start_frame, end_frame)

            print(f"RGP: Playing animation '{animation_item.rgp_name}'")
            return True

        except Exception as e:
            print(f"RGP: Error playing animation: {e}")
            return False

    def stop_animation(self):
        """Stop current animation playback"""
        try:
            if bpy.context.screen.is_animation_playing:
                bpy.ops.screen.animation_cancel()

            self.is_playing = False
            self.current_animation = None
            self.loop_count = 0

            if self.playback_timer:
                self.playback_timer = None

            print("RGP: Animation playback stopped")

        except Exception as e:
            print(f"RGP: Error stopping animation: {e}")

    def pause_animation(self):
        """Pause current animation playback"""
        try:
            if bpy.context.screen.is_animation_playing:
                bpy.ops.screen.animation_cancel()

            self.is_playing = False
            print("RGP: Animation playback paused")

        except Exception as e:
            print(f"RGP: Error pausing animation: {e}")

    def resume_animation(self):
        """Resume paused animation playback"""
        try:
            if not bpy.context.screen.is_animation_playing:
                bpy.ops.screen.animation_play()

            self.is_playing = True
            print("RGP: Animation playback resumed")

        except Exception as e:
            print(f"RGP: Error resuming animation: {e}")

    def play_all_animations(self, animations_list) -> bool:
        """Play all animations in sequence"""
        try:
            scene = bpy.context.scene

            if not animations_list:
                print("RGP: No animations to play")
                return False

            # Calculate total timeline range
            min_start = float('inf')
            max_end = 0

            for anim_item in animations_list:
                if anim_item.rgp_is_active:
                    min_start = min(min_start, anim_item.rgp_start_frame)
                    max_end = max(max_end, anim_item.rgp_end_frame)

            if min_start == float('inf'):
                print("RGP: No active animations found")
                return False

            # Set timeline range
            scene.frame_start = int(min_start)
            scene.frame_end = int(max_end)
            scene.frame_set(int(min_start))

            # Start playback
            if not bpy.context.screen.is_animation_playing:
                bpy.ops.screen.animation_play()

            self.is_playing = True
            print(f"RGP: Playing all animations ({len(animations_list)} total)")
            return True

        except Exception as e:
            print(f"RGP: Error playing all animations: {e}")
            return False

    def apply_speed_multiplier(self, obj: bpy.types.Object, speed: float):
        """Apply speed multiplier to object's animation"""
        if not obj.animation_data or not obj.animation_data.action:
            return

        action = obj.animation_data.action

        for fcurve in action.fcurves:
            for keyframe in fcurve.keyframe_points:
                # Adjust frame timing based on speed
                original_frame = keyframe.co[0]
                new_frame = original_frame / speed
                keyframe.co[0] = new_frame
                keyframe.handle_left[0] = keyframe.handle_left[0] / speed
                keyframe.handle_right[0] = keyframe.handle_right[0] / speed

    def setup_loop_timer(self, start_frame: int, end_frame: int):
        """Setup timer for loop counting"""
        # This would be implemented with Blender's timer system
        # For now, we'll use a simple approach
        duration = (end_frame - start_frame + 1) / bpy.context.scene.render.fps

        def loop_callback():
            if self.current_animation and self.current_animation.rgp_is_looping:
                self.loop_count += 1

                if self.max_loops != -1 and self.loop_count >= self.max_loops:
                    self.stop_animation()
                    return None  # Stop timer
                else:
                    # Continue looping
                    bpy.context.scene.frame_set(start_frame)
                    return duration  # Continue timer

            return None  # Stop timer

        # Register timer (this is a simplified version)
        # In a real implementation, you'd use bpy.app.timers.register
        self.playback_timer = loop_callback

    def get_playback_status(self) -> dict:
        """Get current playback status"""
        return {
            'is_playing': self.is_playing,
            'current_animation': self.current_animation.rgp_name if self.current_animation else None,
            'loop_count': self.loop_count,
            'max_loops': self.max_loops,
            'current_frame': bpy.context.scene.frame_current
        }

# Global playback manager instance
rgp_playback_manager = RGP_PlaybackManager()

def get_playback_manager() -> RGP_PlaybackManager:
    """Get the global playback manager instance"""
    return rgp_playback_manager

def register():
    """Register playback utilities"""
    print("RGP: Playback utilities registered")

def unregister():
    """Unregister playback utilities"""
    print("RGP: Playback utilities unregistered")